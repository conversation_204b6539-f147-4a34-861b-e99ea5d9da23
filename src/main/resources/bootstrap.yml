server:
  port: 8081
spring:
  application:
    name: eflex-service
  profiles:
  # dat环境配置
  #  active: ${ENV:dat}
#  cloud:
#    nacos:
#      config:
  #        server-addr: 10.9.10.42:8848
  #        namespace: 8643ec67-08dd-4f80-8bc9-def107081624
#        file-extension: yaml
  # uat环境配置
  active: ${ENV:uat}
  cloud:
    nacos:
      config:
        server-addr: 172.16.15.102:80
        namespace: 1c48ee57-2992-4f60-9dcc-6a19cd7ee9dd
        file-extension: yaml
#日志配置
logging:
  config: classpath:log.xml
  file: ${server.port}
  level:
    root: info
    com.hqins.ihome: debug
    org.springframework.web: info
    com.alibaba.nacos: warn
    io.grpc.netty.shaded.io.grpc.netty: warn
    org.apache.http: warn
#    走配置文件
#    active: dev,prd,test
