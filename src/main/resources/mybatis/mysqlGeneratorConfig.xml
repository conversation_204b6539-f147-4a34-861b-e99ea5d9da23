<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
  PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <!-- mysql驱动jar包（需要修改为个人jar包所在路径） -->
	<classPathEntry
		location="D:\maven\Repository\mysql\mysql-connector-java\5.1.38\mysql-connector-java-5.1.38.jar" />

	<context id="mysqlTables" defaultModelType="flat" targetRuntime="MyBatis3">
	    <property name="javaFileEncoding" value="UTF-8"/>
		<!-- 为了防止生成的代码中有很多注释，比较难看，加入下面的配置控制 -->
	    <commentGenerator type="org.mybatis.generator.internal.MyCommentGenerator">
			<property name="suppressAllComments" value="false" />
			<property name="suppressDate" value="true" />
	    </commentGenerator>
	    <!-- 注释控制完毕 -->
	    
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="***************************************"
                        userId="eflex"
                        password="eflex"/>

		<!-- 指定生成的类型为java类型，避免数据库中number等类型字段 -->
		<javaTypeResolver>
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>

		<!-- 生成model模型，对应的包，存放位置可以指定具体的路径,如/ProjectName/src，也可以使用MyWebservice/src/main/java来自动生成 -->
		<javaModelGenerator targetPackage="com.sinosoft.eflex.model"
			targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
		</javaModelGenerator>

		<!--对应的xml mapper文件 -->
		<sqlMapGenerator targetPackage="mappers"
			targetProject="src/main/resources">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>

		<!-- 对应的dao接口类 -->
		<javaClientGenerator type="XMLMAPPER"
			targetPackage="com.sinosoft.eflex.dao" targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>
		<!-- tables 配置好表名和对应的实体对象名 -->
        <table tableName="fdcom" domainObjectName="FdCom"
               enableCountByExample="false" enableUpdateByExample="false"
               enableSelectByExample="false" enableDeleteByExample="false"
               selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
            <!-- 主键自增 -->
            <!--<generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
		</table>
	</context>

</generatorConfiguration>