#==============数据源配置==============#
spring:
  datasource:
    name: mydb
    type: com.alibaba.druid.pool.DruidDataSource
    url: ***********************************************************************************************************************************************************************************
    username: prd_uflexdb_w
    password: ylePFyiQbIt6
    driverClassName: com.mysql.jdbc.Driver
    minIdle: 10
    maxActive: 2000
    initialSize: 10
    timeBetweenEvictionRunsMillis: 1800000
    minEvictableIdleTimeMillis: 1800000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
  #==============redis配置==============#
  redis:
    host: r-j5eohxrpprspebc92r.redis.rds.aliyuncs.com
    port: 6379
    password: uys23@qlA5Ul
    timeout: 3000
    database: 0

#==============mybatis配置==============#
mybatis:
  mapperLocations: classpath*:/mappers/**/*.xml
  typeAliasesPackage: com.sinosoft.eflex.model
  configuration:
    call-setters-on-nulls: true

#==============图片展示路径请求==============#
fileDisplay.path: https://uflex.e-hqins.com/api/File/fileDisplay?sftpPath=

#==============log配置==============#     
logging:
  config: classpath:log.xml
  path: /home/<USER>/hqeflex/logs
  file: ${server.port}

#==============pagehelper分页插件配置==============#  
pagehelper:
  helper-dialect: mysql
  supportMethodsArguments: true
  reasonable: true
  params: count=countSql

#==============自定义的属性和值配置==============#     
myProps:
  #==============redis有效期配置==============#
  TermValidity:
    #验证码
    codeValidity: 300
    #登录时长
    loginValidity: 1200
  #核心webservice配置信息（老核心）
  serviceInfo:
    'serviceUrl': "http://lis2.hqins.cn/services/BenefitService?wsdl"
    'operation': "submitData"
  #核心日常计划路径配置信息（新核心）
  coreServiceInfo:
    'basicsPreCheckUrl': "https://ishare-gw.e-hqins.com/service/basicInsureApply-preUW"
    'basicsSignUrl': "https://ishare-gw.e-hqins.com/service/basicInsureApply-dealData"
    'naturalPreCheckUrl': "https://ishare-gw.e-hqins.com/service/singleInsureApply-preUnderWriting"
    'naturalCheckUrl': "https://ishare-gw.e-hqins.com/service/singleInsureApply-dealUnderWriting"
    'naturalSignUrl': "https://ishare-gw.e-hqins.com/service/singleInsureApply-signInsurance"
    'coreAppId': "yf-ocr"
    'coreAppSecret': "xZxQGb7nKMMbJ9cR"
  #电商接口配置信息
  DSserviceInfo:
    'url': "https://gateway.e-hqins.com/"
    'version': "1.0.0"
    'appkey': "27199455-2192-4d14-bbc2-433adfedf139"
    'securityType': "b"
    'publicKey': "d41d8cd98f00b204e9800998ecf8427e"
  #e签宝配置信息
  Etsign:
    'url': "http://openapi2.tsign.cn:8081/realname/rest/external/organ/orgAuth"
    'ProjectID': "**********"
    'ProjectSecret': "4737ef5a9d69beb229f82843d4f7309f"
    #签约接口地址信息
  SignSererUrl:
    SignApplySererUrl: http://172.16.8.225/paymentCardPayService/signApply
    SignConfirmSererUrl: http://172.16.8.225/paymentCardPayService/signConfirm
    Sourse: H08
  #签约接口中台地址信息
  BankSign.MiddlegroundUrl:
    SignApplyUrl: https://t.e-hqins.com/service/api-bankSignApply
    SignConfirmUrl: https://t.e-hqins.com/service/api-bankSignSure
    appId: yf-app
    appSecret: e9b1080bde434da89c9f
  #中台支付中心接口地址信息
  PayCenter:
    transSource: YF
    getPayUrltransCode: ISHARE-PAY-API-GETPAYURL
    payKind: H5
    channelCode: 11
    pageBackUrl: https://uflex.e-hqins.com/phone/#/InsurancePay
    dataBackUrl: https://uflexbk.e-hqins.com/hqeflex/services/payStatusNotice
    payStatusQuery: ISHARE-PAY-API-QUERYPAYSTATUS
    payUrl: https://ishare-gw.e-hqins.com/service/api-getPayUrl
    app_id: yf-ocr
    app_secret: xZxQGb7nKMMbJ9cR
    payQueryUrl: https://ishare-gw.e-hqins.com/service/api-queryPayStatus
  #中台人脸识别接口地址信息
  faceDiscern:
    face_appId: yf-ocr
    face_appSecret: xZxQGb7nKMMbJ9cR
    face_Url: https://ishare-gw.e-hqins.com/intelligence/verify/faceid/wxh5/geturl
    face_QueryUrl: https://ishare-gw.e-hqins.com/intelligence/verify/faceid/wxh5/getdata
    face_transCode: IS_FACEID_001
    face_channelSource: 4
    face_actionType: H5
    face_ruleId: 0
    face_redirectUrl: https://uflex.e-hqins.com/phone/#/InsuranceSignature
  #中台人脸识别接口地址信息
  PrintPlatform:
    guaranteeUrl: http://prd-print.e-hqins.com/DZDYWS/hengqin/dataSearch.action
  #电子投保单生成地址
  GenerateImage:
    policyGenerateImageUrl: http://prd-print.e-hqins.com/DZDYWS/hengqin/generateImage.action
    policyDataSearchUrl: http://prd-print.e-hqins.com/DZDYWS/hengqin/dataSearch.action
  #电子投保单签名地址
  policySign:
    policySignAddress: http://eflex.prd.e-hqins.com/File/dailyFileDisplay?
    policySignBaseAddress: http://eflex.prd.e-hqins.com/File/fileDisplay?
  #==============个人ocr配置==============#
  ocrMsg:
    ocrUrl: https://ishare-gw-uat.e-hqins.com/intelligence/ocr/all
    ocrTransCode: IS_OCR_001
    ocrChannelSource: 7
    ocrActionType: API
    ocrAction: IDCard
    ocrApp_id: yf-ocr
    ocrApp_secret: 77465098786c4cbe8584
  #============微信分享配置 公众号appid、公众号开发者秘钥==============#
  WeChatShareConfig:
    Share_AppId: wx0e300c68021951dd
    Share_AppSecret: 30ed2fc7f76d4d476adc884113950f9a
    GetAccessToken_URL: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    GetTicket_URL: https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi
  #保单打印系统配置--生产
  CASystem:
    #下行--信息发送网关
    caHost: *************
    caPort: 6666
  #==============员福-满天星短信发送平台==============#
  sendMessage:
    url: http://pigeon.e-hqins.com/sendMessage
    product: hengqinlife_staff_benefits
    application: hengqinlife_staff_benefits
    appKey: 38f8769c5ac911eb8d2d70106faecec4
