<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcDailyInsureRiskDetailInfoMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
        <id column="DeployDetalNo" jdbcType="VARCHAR" property="DeployDetalNo"/>
        <result column="DeployNo" jdbcType="VARCHAR" property="DeployNo"/>
        <result column="EnsureCode" jdbcType="VARCHAR" property="EnsureCode"/>
        <result column="RiskCode" jdbcType="VARCHAR" property="RiskCode"/>
        <result column="PlanCode" jdbcType="VARCHAR" property="PlanCode"/>
        <result column="PlanState" jdbcType="VARCHAR" property="PlanState"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="OperatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="Operator"/>
        <result column="MakeDate" jdbcType="DATE" property="MakeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="MakeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="ModifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="ModifyTime"/>
        <result column="PlanUnderwritingStatus" jdbcType="VARCHAR" property="PlanUnderwritingStatus"/>
    </resultMap>
	<sql id="Base_Column_List">
    DeployDetalNo,DeployNo,EnsureCode,RiskCode,PlanCode,PlanState,OperatorCom,Operator,MakeDate,MakeTime,ModifyDate,
    ModifyTime,PlanUnderwritingStatus
  </sql>
	<insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		insert into FcDailyInsureRiskDetailInfo
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="DeployDetalNo != null">
				DeployDetalNo,
			</if>
			<if test="DeployNo != null">
				DeployNo,
			</if>
			<if test="EnsureCode != null">
				EnsureCode,
			</if>
			<if test="RiskCode != null">
				RiskCode,
			</if>
			<if test="PlanCode != null">
				PlanCode,
			</if>
			<if test="PlanState != null">
				PlanState,
			</if>
			<if test="OperatorCom != null">
				OperatorCom,
			</if>
			<if test="Operator != null">
				Operator,
			</if>
			<if test="makeDate != null">
				MakeDate,
			</if>
			<if test="makeTime != null">
				MakeTime,
			</if>
			<if test="modifyDate != null">
				ModifyDate,
			</if>
			<if test="modifyTime != null">
				ModifyTime,
			</if>
			<if test="PlanUnderwritingStatus != null">
			PlanUnderwritingStatus,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="DeployDetalNo != null">
				#{DeployDetalNo,jdbcType=VARCHAR},
			</if>
			<if test="DeployNo != null">
				#{DeployNo,jdbcType=VARCHAR},
			</if>
			<if test="EnsureCode != null">
				#{EnsureCode,jdbcType=VARCHAR},
			</if>
			<if test="RiskCode != null">
				#{RiskCode,jdbcType=VARCHAR},
			</if>
			<if test="PlanCode != null">
				#{PlanCode,jdbcType=VARCHAR},
			</if>
			<if test="PlanState != null">
				#{PlanState,jdbcType=VARCHAR},
			</if>
			<if test="operator != null">
				#{operator,jdbcType=VARCHAR},
			</if>
			<if test="operatorCom != null">
				#{operatorCom,jdbcType=VARCHAR},
			</if>
			<if test="makeDate != null">
				#{makeDate,jdbcType=DATE},
			</if>
			<if test="makeTime != null">
				#{makeTime,jdbcType=VARCHAR},
			</if>
			<if test="modifyDate != null">
				#{modifyDate,jdbcType=DATE},
			</if>
			<if test="modifyTime != null">
				#{modifyTime,jdbcType=VARCHAR},
			</if>
			<if test="PlanUnderwritingStatus != null">
				#{PlanUnderwritingStatus,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		insert into FcDailyInsureRiskDetailInfo
		(DeployDetalNo,
		DeployNo,
		EnsureCode,
		RiskCode,
		PlanCode,
		PlanState,
		OperatorCom,
		Operator,
		MakeDate,
		MakeTime,
		ModifyDate,
		ModifyTime,PlanUnderwritingStatus)
		values (#{DeployDetalNo,jdbcType=VARCHAR},
		#{DeployNo,jdbcType=VARCHAR},
		#{EnsureCode,jdbcType=VARCHAR},
		#{RiskCode,jdbcType=VARCHAR},
		#{PlanCode,jdbcType=VARCHAR},
		#{PlanState,jdbcType=VARCHAR},
		#{OperatorCom,jdbcType=VARCHAR},
		#{Operator,jdbcType=VARCHAR},
		#{MakeDate,jdbcType=DATE},
		#{MakeTime,jdbcType=VARCHAR},
		#{ModifyDate,jdbcType=DATE},
		#{ModifyTime,jdbcType=VARCHAR},
		#{PlanUnderwritingStatus,jdbcType=VARCHAR})
	</insert>

	<select id='selectListBydailyPlanCode' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		SELECT  a.DeployDetalNo,
				a.DeployNo,
				a.EnsureCode,
				a.RiskCode,
				a.PlanCode,
				a.PlanState,
				a.OperatorCom,
				a.Operator,
				a.MakeDate,
				a.MakeTime,
				a.ModifyDate,
				a.ModifyTime,a.PlanUnderwritingStatus
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
	</select>
	<select id='selectByCodeAndstate' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		SELECT  <include refid="Base_Column_List" />
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
		and a.PlanState='0'
	    ORDER BY planCode DESC
	</select>
	<select id='selectByCodeAndstateAll' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		(SELECT  a.DeployDetalNo,
				a.DeployNo,
				a.EnsureCode,
				a.RiskCode,
				a.PlanCode,
				a.PlanState,
				a.OperatorCom,
				a.Operator,
				a.MakeDate,
				a.MakeTime,
				a.ModifyDate,
				a.ModifyTime,
				a.PlanUnderwritingStatus
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
		and a.PlanState='0'
		and a.PlanUnderwritingStatus='1')
UNION
		(SELECT  a.DeployDetalNo,
				a.DeployNo,
				a.EnsureCode,
				a.RiskCode,
				a.PlanCode,
				a.PlanState,
				a.OperatorCom,
				a.Operator,
				a.MakeDate,
				a.MakeTime,
				a.ModifyDate,
				a.ModifyTime,a.PlanUnderwritingStatus
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
		and a.PlanState in ('0','1')
		and a.PlanUnderwritingStatus ='3')
	</select>
	<select id='selectByCodeAndstate013' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		SELECT  a.DeployDetalNo,
				a.DeployNo,
				a.EnsureCode,
				a.RiskCode,
				a.PlanCode,
				a.PlanState,
				a.OperatorCom,
				a.Operator,
				a.MakeDate,
				a.MakeTime,
				a.ModifyDate,
				a.ModifyTime,a.PlanUnderwritingStatus
        FROM FcDailyInsureRiskDetailInfo a
        inner join fcensure b on a.EnsureCode=b.EnsureCode
		where a.EnsureCode = #{EnsureCode}
		and a.PlanState='0'
	</select>
	<update id="updatePlanCodeBydeployDetalNo" parameterType="java.lang.String">
		update FcDailyInsureRiskDetailInfo
		set PlanState='1'
		where DeployDetalNo = #{DeployDetalNo,jdbcType=VARCHAR}
	</update>
	<update id="updatePlanCodeBydeployDetalYes" parameterType="java.lang.String">
		update FcDailyInsureRiskDetailInfo
		set PlanState='0'
		where DeployDetalNo = #{DeployDetalNo,jdbcType=VARCHAR}
	</update>
	<select id='selectByCodeAndEnsureCode' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		SELECT  a.DeployDetalNo,
				a.DeployNo,
				a.EnsureCode,
				a.RiskCode,
				a.PlanCode,
				a.PlanState,
				a.OperatorCom,
				a.Operator,
				a.MakeDate,
				a.MakeTime,
				a.ModifyDate,
				a.ModifyTime,a.PlanUnderwritingStatus
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
		and 	a.PlanCode= #{PlanCode}
		and  a.planState = '0'
	</select>
	<select id='selectByCodeAndstate_Update' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		SELECT  a.DeployDetalNo,
				a.DeployNo,
				a.EnsureCode,
				a.RiskCode,
				a.PlanCode,
				a.PlanState,
				a.OperatorCom,
				a.Operator,
				a.MakeDate,
				a.MakeTime,
				a.ModifyDate,
				a.ModifyTime,a.PlanUnderwritingStatus
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
		and a.PlanState='1'
		and a.PlanUnderwritingStatus='3'
	</select>
	<select id='selectByCodeAndstate_success' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		SELECT  a.DeployDetalNo,
				a.DeployNo,
				a.EnsureCode,
				a.RiskCode,
				a.PlanCode,
				a.PlanState,
				a.OperatorCom,
				a.Operator,
				a.MakeDate,
				a.MakeTime,
				a.ModifyDate,
				a.ModifyTime,a.PlanUnderwritingStatus
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
		and a.PlanState='0'
		and a.PlanUnderwritingStatus='1'
	</select>
	<select id='selectBycodeandPlanCode' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		SELECT <include refid="Base_Column_List" />
        FROM FcDailyInsureRiskDetailInfo a
		where a.EnsureCode = #{EnsureCode}
		and a.PlanCode=#{PlanCode}
	</select>
	<select id='selectPlanCount'  resultType="java.util.Map">
		select PlanCode,count(*) as count
		from FcDailyInsureRiskDetailInfo
		WHERE PlanCode IN('ID6380','ID6381','ID6382','ID6383') AND PlanState='0'
		group by PlanCode
	</select>
<select id='selectInsureAmount'  resultType="java.util.Map">
		select DutyCode ,sum(InsuredAmount) as count
		from fcorderitemdetail
		WHERE DutyCode IN ('ID6380','ID6381','ID6382','ID6383')
		group by DutyCode
	</select>
	<delete id="deleteByEnsureCode" parameterType="java.lang.String">
    delete from FcDailyInsureRiskDetailInfo
    where EnsureCode = #{EnsureCode,jdbcType=VARCHAR}
  </delete>
	<update id="updatePlanCodeTobad" parameterType="java.lang.String">
		update FcDailyInsureRiskDetailInfo
		set PlanState='1'
		where EnsureCode = #{EnsureCode,jdbcType=VARCHAR}
	</update>
	<select id='selectByPlanCodeAndEnsureCode'  parameterType="java.lang.String" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM FcDailyInsureRiskDetailInfo
		WHERE EnsureCode = #{EnsureCode,jdbcType=VARCHAR}
		and 	PlanCode = #{PlanCode,jdbcType=VARCHAR}
		and 	RiskCode = #{RiskCode,jdbcType=VARCHAR}
	</select>
	<update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo">
		update FcDailyInsureRiskDetailInfo
		<set>
			<if test="DeployDetalNo != null">
				DeployDetalNo = #{DeployDetalNo,jdbcType=VARCHAR},
			</if>
			<if test="DeployNo != null">
				DeployNo = #{DeployNo,jdbcType=VARCHAR},
			</if>
			<if test="EnsureCode != null">
				EnsureCode = #{EnsureCode,jdbcType=VARCHAR},
			</if>
			<if test="RiskCode != null">
				RiskCode = #{RiskCode,jdbcType=VARCHAR},
			</if>
			<if test="PlanCode != null">
				PlanCode = #{PlanCode,jdbcType=VARCHAR},
			</if>
			<if test="PlanState != null">
				PlanState = #{PlanState,jdbcType=VARCHAR},
			</if>
			<if test="OperatorCom != null">
				OperatorCom = #{OperatorCom,jdbcType=VARCHAR},
			</if>
			<if test="Operator != null">
				Operator = #{Operator,jdbcType=VARCHAR},
			</if>
			<if test="MakeDate != null">
				MakeDate = #{MakeDate,jdbcType=DATE},
			</if>
			<if test="MakeTime != null">
				MakeTime = #{MakeTime,jdbcType=VARCHAR},
			</if>
			<if test="ModifyDate != null">
				ModifyDate = #{ModifyDate,jdbcType=DATE},
			</if>
			<if test="ModifyTime != null">
				ModifyTime = #{ModifyTime,jdbcType=VARCHAR},
			</if>
			<if test="PlanUnderwritingStatus != null">
				PlanUnderwritingStatus = #{PlanUnderwritingStatus,jdbcType=VARCHAR},
			</if>
		</set>
		where DeployDetalNo = #{DeployDetalNo,jdbcType=VARCHAR}
	</update>
	<select id='checkisHadBaseIntheGrp'  parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM 	fcensure fc
		INNER JOIN 	fcdailyinsureriskdetailinfo fd ON fc.`EnsureCode`=fd.`EnsureCode`
		INNER JOIN	fdriskplaninfo	fp ON fp.`PlanCode`=fd.`PlanCode`
		WHERE 	fc.`GrpNo`=#{grpNo}
		AND 	fc.`PlanType`='2'
		AND	fp.`RiskCode`=#{RiskCode}
		AND	fc.`EnsureState`='015'
	</select>
	<select id="selectDailyPlans" resultType="java.util.Map">
		select f1.PlanCode planCode,
			   f2.PlanName planName
		from fcdailyinsureriskdetailinfo f1 inner join fdriskplaninfo f2 on f1.PlanCode = f2.PlanCode
			and f1.EnsureCode = #{ensureCode} and f1.PlanState = '0' and f1.PlanUnderwritingStatus = '1'
	</select>


</mapper>