<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorAddPlanRiskDutyInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorAddPlanRiskDutyInfo">
    <id column="EdorAddPlanSN" jdbcType="VARCHAR" property="edorAddPlanSN" />
    <id column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <id column="DutyCode" jdbcType="VARCHAR" property="dutyCode" />
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
    <result column="DutyName" jdbcType="VARCHAR" property="dutyName" />
    <result column="DutyAmnt" jdbcType="DECIMAL" property="dutyAmnt" />
    <result column="DutyPrem" jdbcType="DECIMAL" property="dutyPrem" />
    <result column="GetLimit" jdbcType="DOUBLE" property="getLimit" />
    <result column="GetRate" jdbcType="DOUBLE" property="getRate" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EdorAddPlanSN, RiskCode, DutyCode, DutyName, DutyAmnt, DutyPrem, GetLimit, GetRate, 
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedoraddplanriskdutyinfo
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcedoraddplanriskdutyinfo
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskDutyInfo">
    insert into fcedoraddplanriskdutyinfo (EdorAddPlanSN, RiskCode, DutyCode, 
      DutyName, DutyAmnt, DutyPrem, 
      GetLimit, GetRate, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{edorAddPlanSN,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, 
      #{dutyName,jdbcType=VARCHAR}, #{dutyAmnt,jdbcType=DECIMAL}, #{dutyPrem,jdbcType=DECIMAL}, 
      #{getLimit,jdbcType=DECIMAL}, #{getRate,jdbcType=DECIMAL}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskDutyInfo">
    insert into fcedoraddplanriskdutyinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edorAddPlanSN != null">
        EdorAddPlanSN,
      </if>
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="dutyCode != null">
        DutyCode,
      </if>
      <if test="dutyName != null">
        DutyName,
      </if>
      <if test="dutyAmnt != null">
        DutyAmnt,
      </if>
      <if test="dutyPrem != null">
        DutyPrem,
      </if>
      <if test="getLimit != null">
        GetLimit,
      </if>
      <if test="getRate != null">
        GetRate,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edorAddPlanSN != null">
        #{edorAddPlanSN,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyCode != null">
        #{dutyCode,jdbcType=VARCHAR},
      </if>
      <if test="dutyName != null">
        #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="dutyAmnt != null">
        #{dutyAmnt,jdbcType=DECIMAL},
      </if>
      <if test="dutyPrem != null">
        #{dutyPrem,jdbcType=DECIMAL},
      </if>
      <if test="getLimit != null">
        #{getLimit,jdbcType=DECIMAL},
      </if>
      <if test="getRate != null">
        #{getRate,jdbcType=DECIMAL},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskDutyInfo">
    update fcedoraddplanriskdutyinfo
    <set>
      <if test="dutyName != null">
        DutyName = #{dutyName,jdbcType=VARCHAR},
      </if>
      <if test="dutyAmnt != null">
        DutyAmnt = #{dutyAmnt,jdbcType=DECIMAL},
      </if>
      <if test="dutyPrem != null">
        DutyPrem = #{dutyPrem,jdbcType=DECIMAL},
      </if>
      <if test="getLimit != null">
        GetLimit = #{getLimit,jdbcType=DECIMAL},
      </if>
      <if test="getRate != null">
        GetRate = #{getRate,jdbcType=DECIMAL},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorAddPlanRiskDutyInfo">
    update fcedoraddplanriskdutyinfo
    set DutyName = #{dutyName,jdbcType=VARCHAR},
      DutyAmnt = #{dutyAmnt,jdbcType=DECIMAL},
      DutyPrem = #{dutyPrem,jdbcType=DECIMAL},
      GetLimit = #{getLimit,jdbcType=DECIMAL},
      GetRate = #{getRate,jdbcType=DECIMAL},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR}
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
      and DutyCode = #{dutyCode,jdbcType=VARCHAR}
  </update>

  <insert id="insertEdorAddPlanRiskDutyList" parameterType="java.util.List">
    insert into fcedoraddplanriskdutyinfo
    (EdorAddPlanSN,RiskCode,DutyCode,GrpContNo,DutyName,DutyAmnt,DutyPrem,GetLimit,GetRate,Operator,OperatorCom,MakeDate,MakeTime,ModifyDate,ModifyTime)
    VALUES <foreach collection="list"  item="item" index="index" open="" close="" separator=",">
    (#{list[${index}].edorAddPlanSN,jdbcType=VARCHAR},
    #{list[${index}].riskCode,jdbcType=VARCHAR},
    #{list[${index}].dutyCode,jdbcType=VARCHAR},
    #{list[${index}].grpContNo,jdbcType=VARCHAR},
    #{list[${index}].dutyName,jdbcType=VARCHAR},
    #{list[${index}].dutyAmnt,jdbcType=DECIMAL},
    #{list[${index}].dutyPrem,jdbcType=DECIMAL},
    #{list[${index}].getLimit,jdbcType=DOUBLE},
    #{list[${index}].getRate,jdbcType=DOUBLE},
    #{list[${index}].operator,jdbcType=VARCHAR},
    #{list[${index}].operatorCom,jdbcType=VARCHAR},
    #{list[${index}].makeDate,jdbcType=DATE},
    #{list[${index}].makeTime,jdbcType=VARCHAR},
    #{list[${index}].modifyDate,jdbcType=DATE},
    #{list[${index}].modifyTime,jdbcType=VARCHAR})
  </foreach>
  </insert>

  <select id="getDutyInfoByGrpCotNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEdorAddPlanRiskDutyInfo">
    SELECT
    <include refid="Base_Column_List" />
    FROM FCEdorAddPlanRiskDutyInfo WHERE GrpContNo = #{grpContNo,jdbcType=VARCHAR}
  </select>

  <select id="getDutyInfoByPlanSNAndRiskCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEdorAddPlanRiskDutyInfo">
    SELECT
      <include refid="Base_Column_List" />
        FROM FCEdorAddPlanRiskDutyInfo WHERE EdorAddPlanSN = #{edorAddPlanSN,jdbcType=VARCHAR} AND RiskCode = #{riskCode,jdbcType=VARCHAR}
  </select>
    <delete id="deleteByGrpContNo" parameterType="java.lang.String">
        delete from FCEdorAddPlanRiskDutyInfo
        where grpContNo = #{grpContNo}
    </delete>
</mapper>