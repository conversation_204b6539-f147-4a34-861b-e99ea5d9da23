<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcInsureEflexPlanOptionalMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcInsureEflexPlanOptional">
    <id column="OrderItemDetailNo" jdbcType="VARCHAR" property="orderItemDetailNo" />
    <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <id column="OptDutyCode" jdbcType="VARCHAR" property="optDutyCode" />
    <result column="prem" jdbcType="DOUBLE" property="prem" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    OrderItemDetailNo, AmountGrageCode, OptDutyCode, prem, Operator, OperatorCom, MakeDate, 
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcinsureeflexplanoptional
    where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcinsureeflexplanoptional
    where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlanOptional">
    insert into fcinsureeflexplanoptional (OrderItemDetailNo, AmountGrageCode, 
      OptDutyCode, prem, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{orderItemDetailNo,jdbcType=VARCHAR}, #{amountGrageCode,jdbcType=VARCHAR}, 
      #{optDutyCode,jdbcType=VARCHAR}, #{prem,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlanOptional">
    insert into fcinsureeflexplanoptional
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderItemDetailNo != null">
        OrderItemDetailNo,
      </if>
      <if test="amountGrageCode != null">
        AmountGrageCode,
      </if>
      <if test="optDutyCode != null">
        OptDutyCode,
      </if>
      <if test="prem != null">
        prem,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderItemDetailNo != null">
        #{orderItemDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="amountGrageCode != null">
        #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="optDutyCode != null">
        #{optDutyCode,jdbcType=VARCHAR},
      </if>
      <if test="prem != null">
        #{prem,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlanOptional">
    update fcinsureeflexplanoptional
    <set>
      <if test="prem != null">
        prem = #{prem,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcInsureEflexPlanOptional">
    update fcinsureeflexplanoptional
    set prem = #{prem,jdbcType=DOUBLE},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderItemDetailNo = #{orderItemDetailNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </update>
  <delete id="deleteByOrderNo" parameterType="java.lang.String">
    DELETE FROM FcInsureEflexPlanOptional WHERE OrderItemDetailNo IN (SELECT OrderItemDetailNo FROM fcorderitem WHERE OrderNo = #{orderNo})
  </delete>
</mapper>