<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCAppntImpartInfoPersonalMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCAppntImpartInfoPersonal">
    <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo" />
    <id column="ImpartCode" jdbcType="VARCHAR" property="impartCode" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="ImpartParamModle" jdbcType="VARCHAR" property="impartParamModle" />
    <result column="DsimpartParam" jdbcType="VARCHAR" property="dsimpartParam" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    OrderItemNo, ImpartCode, GrpNo, ImpartParamModle,DsimpartParam, Operator, OperatorCom, MakeDate,
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fcappntimpartinfopersonal
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByOrderItemNo" resultType="com.sinosoft.eflex.model.dailyplan.AppntImpart">
    select f1.ImpartVer impartVer,
           f1.ImpartContent impartContent,
           f2.ImpartCode impartCode,
           f2.ImpartParamModle impartReply,
          f2.dsimpartparam introDuctions
    from fdimpartinfopersonal f1
           inner join fcappntimpartinfopersonal f2 on f1.ImpartCode = f2.ImpartCode
      and f2.OrderItemNo = #{orderItemNo}
    and f1.ImpartCode not in ('00000','00001')
  </select>
 <select id="selectByOrderItemNos" resultType="com.sinosoft.eflex.model.dailyplan.AppntImpart">
    select f1.ImpartVer impartVer,
           f1.ImpartContent impartContent,
           f2.ImpartCode impartCode,
           f2.ImpartParamModle impartReply,
          f2.dsimpartparam introDuctions
    from fdimpartinfopersonal f1
           inner join fcappntimpartinfopersonal f2 on f1.ImpartCode = f2.ImpartCode
      and f2.OrderItemNo = #{orderItemNo}
    and f1.ImpartCode not in ('00000','00001','11110-A','11110-B','11110-C')
  </select>
 <select id="selectByOrderItemNoAndCode" resultType="com.sinosoft.eflex.model.dailyplan.AppntImpart">
    select f1.ImpartVer impartVer,
           f1.ImpartContent impartContent,
           f2.ImpartCode impartCode,
           f2.ImpartParamModle impartReply,
          f2.dsimpartparam introDuctions
    from fdimpartinfopersonal f1
           inner join fcappntimpartinfopersonal f2 on f1.ImpartCode = f2.ImpartCode
      and f2.OrderItemNo = #{orderItemNo}
    and f1.ImpartCode  ='11110-A'
  </select>
  <select id="selectAllByOrderItemNo" resultType="com.sinosoft.eflex.model.FCAppntImpartInfoPersonal">
    select ImpartCode impartCode,
           ImpartParamModle impartParamModle
    from fcappntimpartinfopersonal where OrderItemNo = #{orderItemNo}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcappntimpartinfopersonal
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteImpartInfo" parameterType="java.lang.String">
    delete from fcappntimpartinfopersonal
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfoPersonal">
    insert into fcappntimpartinfopersonal (OrderItemNo, ImpartCode, GrpNo,
      ImpartParamModle,  DsimpartParam, Operator, OperatorCom,
      MakeDate, MakeTime, ModifyDate,
      ModifyTime)
    values (#{orderItemNo,jdbcType=VARCHAR}, #{impartCode,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR},
      #{impartParamModle,jdbcType=VARCHAR}, #{dsimpartParam,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfoPersonal">
    insert into fcappntimpartinfopersonal
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderItemNo != null">
        OrderItemNo,
      </if>
      <if test="impartCode != null">
        ImpartCode,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="impartParamModle != null">
        ImpartParamModle,
      </if>
      <if test="dsimpartParam != null">
        DsimpartParam,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderItemNo != null">
        #{orderItemNo,jdbcType=VARCHAR},
      </if>
      <if test="impartCode != null">
        #{impartCode,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="impartParamModle != null">
        #{impartParamModle,jdbcType=VARCHAR},
      </if>
     <if test="dsimpartParam != null">
        #{dsimpartParam,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfoPersonal">
    update fcappntimpartinfopersonal
    <set>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="impartParamModle != null">
        ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR},
      </if>
      <if test="dsimpartParam != null">
        DsimpartParam = #{dsimpartParam,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
<update id="updateByModle" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfoPersonal">
    update fcappntimpartinfopersonal
    <set>
      <if test="dsimpartParam != null">
        DsimpartParam = #{dsimpartParam,jdbcType=VARCHAR},
      </if>
    </set>
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCAppntImpartInfoPersonal">
    update fcappntimpartinfopersonal
    set GrpNo = #{grpNo,jdbcType=VARCHAR},
      ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR},DsimpartParam = #{dsimpartParam,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
  <select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
     ImpartCode, 
     case when ImpartCode in ('11103-A','11103-B','11103-C','11104','11105-A','11105-B','11105-C','11105-D','11105-E','11105-F','11105-G','11105-H','11105-I','11106','11107','11108','11109-B','11109-C','11110-B','11110-C','11204','11205') and ImpartParamModle like '%是%'
               then CONCAT(ImpartParamModle,DsimpartParam)
               else ImpartParamModle END AS ImpartParamModle
    from fcappntimpartinfopersonal
    where 1=1
    <if test="orderItemNo != null and orderItemNo != ''">
      and OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>