<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcDutyGradeOptionalAmountInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo">
    <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <id column="OptDutyCode" jdbcType="VARCHAR" property="optDutyCode" />
    <result column="Amnt" jdbcType="DOUBLE" property="amnt" />
    <result column="Prem" jdbcType="DOUBLE" property="prem" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    AmountGrageCode, OptDutyCode, Amnt, Prem, maxGetDay, Operator, OperatorCom, MakeDate, MakeTime,
    ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.util.Map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcdutygradeoptionalamountinfo
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      <if test=" optDutyCode != null and optDutyCode != '' ">
        and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
      </if>
  </select>
  <select id="selectOptDutyCodeList" parameterType="java.lang.String" resultType="java.lang.String">
    select 
    AmountGrageCode
    from fcdutygradeoptionalamountinfo
    where AmountGrageCode = #{AmountGrageCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcdutygradeoptionalamountinfo
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo">
    insert into fcdutygradeoptionalamountinfo (AmountGrageCode, OptDutyCode, Amnt, 
      Prem, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{amountGrageCode,jdbcType=VARCHAR}, #{optDutyCode,jdbcType=VARCHAR}, #{amnt,jdbcType=DOUBLE},
      #{prem,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo">
    insert into fcdutygradeoptionalamountinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="amountGrageCode != null">
        AmountGrageCode,
      </if>
      <if test="optDutyCode != null">
        OptDutyCode,
      </if>
      <if test="amnt != null">
        Amnt,
      </if>
      <if test="prem != null">
        Prem,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="amountGrageCode != null">
        #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="optDutyCode != null">
        #{optDutyCode,jdbcType=VARCHAR},
      </if>
      <if test="amnt != null">
        #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="prem != null">
        #{prem,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo">
    update fcdutygradeoptionalamountinfo
    <set>
      <if test="amnt != null">
        Amnt = #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="prem != null">
        Prem = #{prem,jdbcType=DOUBLE},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo">
    update fcdutygradeoptionalamountinfo
    set Amnt = #{amnt,jdbcType=DOUBLE},
      Prem = #{prem,jdbcType=DOUBLE},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </update>

  <delete id="deleteByEnsureCode" parameterType="java.util.Map">
    delete from fcdutygradeoptionalamountinfo
    where AmountGrageCode in (select AmountGrageCode FROM fcdutyamountgrade where ensureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test=" riskCode != null and riskCode != ''">
      and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </if>
    <if test=" riskType != null and riskType != ''">
      and RiskType = #{riskType,jdbcType=VARCHAR}
    </if>
    <if test=" amountGrageCode != null and amountGrageCode != '' ">
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    </if>)
  </delete>

  <insert id="insertOptDutyList" parameterType="java.util.List">
      insert into fcdutygradeoptionalamountinfo
      (AmountGrageCode,OptDutyCode,Amnt,maxGetDay,Operator,MakeDate,MakeTime,ModifyDate,ModifyTime)
      VALUES <foreach collection="list"  item="item" index="index" open="" close="" separator=",">
    (#{list[${index}].amountGrageCode,jdbcType=VARCHAR},
        #{list[${index}].optDutyCode,jdbcType=VARCHAR},
        #{list[${index}].amnt,jdbcType=VARCHAR},
        #{list[${index}].maxGetDay,jdbcType=DECIMAL},
        #{list[${index}].operator,jdbcType=VARCHAR},
        #{list[${index}].makeDate,jdbcType=DATE},
        #{list[${index}].makeTime,jdbcType=VARCHAR},
        #{list[${index}].modifyDate,jdbcType=DATE},
        #{list[${index}].modifyTime,jdbcType=VARCHAR})
      </foreach>
  </insert>

  <update id="updateOptDutyList" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="" close="" separator=",">
      update fcdutygradeoptionalamountinfo
      <set>
        Amnt = #{item.amnt},
        ModifyDate = #{item.modifyDate,jdbcType=VARCHAR},
        ModifyTime = #{item.modifyTime,jdbcType=VARCHAR}
      </set>
      where AmountGrageCode = #{item.amountGrageCode}
      and OptDutyCode = #{item.optDutyCode}
    </foreach>
  </update>
</mapper>