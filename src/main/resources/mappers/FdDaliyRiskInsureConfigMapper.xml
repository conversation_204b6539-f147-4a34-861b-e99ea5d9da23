<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FdDaliyRiskInsureConfigMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCAppntImpartInfo">
        <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
        <id column="ImpartCode" jdbcType="VARCHAR" property="impartCode" />
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
        <result column="ImpartParamModle" jdbcType="VARCHAR" property="impartParamModle" />
        <result column="Operator" jdbcType="VARCHAR" property="operator" />
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
        <result column="MakeDate" jdbcType="DATE" property="makeDate" />
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    </resultMap>

    <select id="selectDailyInsurePeriodInfo" parameterType="com.sinosoft.eflex.model.dailyplan.DailyInsureInfo" resultType="java.util.Map">
        select CodeKey,CodeName
        from  fdcode
        where codeType = 'DailyInsurePeriod'
        and FIND_IN_SET(CodeKey,(select distinct DailyInsurePeriodType from fddaliyriskinsureconfig WHERE #{age,jdbcType=VARCHAR} BETWEEN agedown and ageup and RiskCode = #{riskCode,jdbcType=VARCHAR}))
    </select>

    <select id="selectPaymentFrequencyInfo" parameterType="com.sinosoft.eflex.model.dailyplan.DailyInsureInfo" resultType="java.util.Map">
        SELECT CodeKey,CodeName
            FROM fdcode
            WHERE CodeType = 'PaymentFrequency' and FIND_IN_SET(CodeKey,(SELECT group_concat(PaymentFrequencyType)
						from fddaliyriskinsureconfig
						WHERE FIND_IN_SET(#{dailyInsurePeriodType,jdbcType=VARCHAR},DailyInsurePeriodType)
						and #{age,jdbcType=VARCHAR} BETWEEN agedown and ageup
						and RiskCode = #{riskCode,jdbcType=VARCHAR}))
    </select>

    <select id="selectPaymentPeriodInfo" parameterType="com.sinosoft.eflex.model.dailyplan.DailyInsureInfo" resultType="java.util.Map">
        SELECT CodeKey,CodeName
            FROM fdcode
            WHERE codeType = 'PaymentPeriod' and FIND_IN_SET(CodeKey,(SELECT PaymentPeriodType
                  from fddaliyriskinsureconfig
                  WHERE FIND_IN_SET(#{dailyInsurePeriodType,jdbcType=VARCHAR},DailyInsurePeriodType)
                  and FIND_IN_SET(#{paymentFrequencyType,jdbcType=VARCHAR},PaymentFrequencyType)
                  and riskCode = #{riskCode,jdbcType=VARCHAR} and #{age,jdbcType=VARCHAR} BETWEEN AgeDown and AgeUp))
    </select>

</mapper>