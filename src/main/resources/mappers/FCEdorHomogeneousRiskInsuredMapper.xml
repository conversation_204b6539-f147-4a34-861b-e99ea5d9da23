<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorHomogeneousRiskInsuredMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured">
    <id column="HomogeneousRiskInsuredSN" jdbcType="VARCHAR" property="homogeneousRiskInsuredSN" />
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
    <result column="Batch" jdbcType="VARCHAR" property="batch" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="oldName" jdbcType="VARCHAR" property="oldName" />
    <result column="oldIdType" jdbcType="VARCHAR" property="oldIdType" />
    <result column="oldIdNo" jdbcType="VARCHAR" property="oldIdNo" />
    <result column="oldSex" jdbcType="VARCHAR" property="oldSex" />
    <result column="oldBirthday" jdbcType="VARCHAR" property="oldBirthday" />
    <result column="newName" jdbcType="VARCHAR" property="newName" />
    <result column="newNativeplace" jdbcType="VARCHAR" property="newNativeplace" />
    <result column="newIdType" jdbcType="VARCHAR" property="newIdType" />
    <result column="newIdNo" jdbcType="VARCHAR" property="newIdNo" />
    <result column="newIdTypeEndDate" jdbcType="DATE" property="newIdTypeEndDate" />
    <result column="newSex" jdbcType="VARCHAR" property="newSex" />
    <result column="newBirthday" jdbcType="VARCHAR" property="newBirthday" />
    <result column="newMobilePhone" jdbcType="VARCHAR" property="newMobilePhone" />
    <result column="newJobType" jdbcType="VARCHAR" property="newJobType" />
    <result column="newJobCode" jdbcType="VARCHAR" property="newJobCode" />
    <result column="newJoinMedProtect" jdbcType="VARCHAR" property="newJoinMedProtect" />
    <result column="nzValidate" jdbcType="VARCHAR" property="nzValidate" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    HomogeneousRiskInsuredSN, GrpContNo, Batch, GrpNo, oldName, oldIdType, oldIdNo, oldSex, 
    oldBirthday, newName, newNativeplace, newIdType, newIdNo, newIdTypeEndDate, newSex, 
    newBirthday, newMobilePhone, newJobType, newJobCode, newJoinMedProtect, nzValidate,Operator,
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedorhomogeneousriskinsured
    where HomogeneousRiskInsuredSN = #{homogeneousRiskInsuredSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcedorhomogeneousriskinsured
    where HomogeneousRiskInsuredSN = #{homogeneousRiskInsuredSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured">
    insert into fcedorhomogeneousriskinsured (HomogeneousRiskInsuredSN, GrpContNo, 
      Batch, GrpNo, oldName, 
      oldIdType, oldIdNo, oldSex, 
      oldBirthday, newName, newNativeplace, 
      newIdType, newIdNo, newIdTypeEndDate, 
      newSex, newBirthday, newMobilePhone, 
      newJobType, newJobCode, newJoinMedProtect, nzValidate,
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{homogeneousRiskInsuredSN,jdbcType=VARCHAR}, #{grpContNo,jdbcType=VARCHAR}, 
      #{batch,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, #{oldName,jdbcType=VARCHAR}, 
      #{oldIdType,jdbcType=VARCHAR}, #{oldIdNo,jdbcType=VARCHAR}, #{oldSex,jdbcType=VARCHAR}, 
      #{oldBirthday,jdbcType=VARCHAR}, #{newName,jdbcType=VARCHAR}, #{newNativeplace,jdbcType=VARCHAR}, 
      #{newIdType,jdbcType=VARCHAR}, #{newIdNo,jdbcType=VARCHAR}, #{newIdTypeEndDate,jdbcType=DATE}, 
      #{newSex,jdbcType=VARCHAR}, #{newBirthday,jdbcType=VARCHAR}, #{newMobilePhone,jdbcType=VARCHAR}, 
      #{newJobType,jdbcType=VARCHAR}, #{newJobCode,jdbcType=VARCHAR}, #{newJoinMedProtect,jdbcType=VARCHAR},  #{nzValidate,jdbcType=VARCHAR},
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured">
    insert into fcedorhomogeneousriskinsured
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="homogeneousRiskInsuredSN != null">
        HomogeneousRiskInsuredSN,
      </if>
      <if test="grpContNo != null">
        GrpContNo,
      </if>
      <if test="batch != null">
        Batch,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="oldName != null">
        oldName,
      </if>
      <if test="oldIdType != null">
        oldIdType,
      </if>
      <if test="oldIdNo != null">
        oldIdNo,
      </if>
      <if test="oldSex != null">
        oldSex,
      </if>
      <if test="oldBirthday != null">
        oldBirthday,
      </if>
      <if test="newName != null">
        newName,
      </if>
      <if test="newNativeplace != null">
        newNativeplace,
      </if>
      <if test="newIdType != null">
        newIdType,
      </if>
      <if test="newIdNo != null">
        newIdNo,
      </if>
      <if test="newIdTypeEndDate != null">
        newIdTypeEndDate,
      </if>
      <if test="newSex != null">
        newSex,
      </if>
      <if test="newBirthday != null">
        newBirthday,
      </if>
      <if test="newMobilePhone != null">
        newMobilePhone,
      </if>
      <if test="newJobType != null">
        newJobType,
      </if>
      <if test="newJobCode != null">
        newJobCode,
      </if>
      <if test="newJoinMedProtect != null">
        newJoinMedProtect,
      </if>
      <if test="nzValidate != null">
          nzValidate,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="homogeneousRiskInsuredSN != null">
        #{homogeneousRiskInsuredSN,jdbcType=VARCHAR},
      </if>
      <if test="grpContNo != null">
        #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="oldName != null">
        #{oldName,jdbcType=VARCHAR},
      </if>
      <if test="oldIdType != null">
        #{oldIdType,jdbcType=VARCHAR},
      </if>
      <if test="oldIdNo != null">
        #{oldIdNo,jdbcType=VARCHAR},
      </if>
      <if test="oldSex != null">
        #{oldSex,jdbcType=VARCHAR},
      </if>
      <if test="oldBirthday != null">
        #{oldBirthday,jdbcType=VARCHAR},
      </if>
      <if test="newName != null">
        #{newName,jdbcType=VARCHAR},
      </if>
      <if test="newNativeplace != null">
        #{newNativeplace,jdbcType=VARCHAR},
      </if>
      <if test="newIdType != null">
        #{newIdType,jdbcType=VARCHAR},
      </if>
      <if test="newIdNo != null">
        #{newIdNo,jdbcType=VARCHAR},
      </if>
      <if test="newIdTypeEndDate != null">
        #{newIdTypeEndDate,jdbcType=DATE},
      </if>
      <if test="newSex != null">
        #{newSex,jdbcType=VARCHAR},
      </if>
      <if test="newBirthday != null">
        #{newBirthday,jdbcType=VARCHAR},
      </if>
      <if test="newMobilePhone != null">
        #{newMobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="newJobType != null">
        #{newJobType,jdbcType=VARCHAR},
      </if>
      <if test="newJobCode != null">
        #{newJobCode,jdbcType=VARCHAR},
      </if>
      <if test="newJoinMedProtect != null">
        #{newJoinMedProtect,jdbcType=VARCHAR},
      </if>
      <if test="nzValidate != null">
          #{nzValidate,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured">
    update fcedorhomogeneousriskinsured
    <set>
      <if test="grpContNo != null">
        GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        Batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="oldName != null">
        oldName = #{oldName,jdbcType=VARCHAR},
      </if>
      <if test="oldIdType != null">
        oldIdType = #{oldIdType,jdbcType=VARCHAR},
      </if>
      <if test="oldIdNo != null">
        oldIdNo = #{oldIdNo,jdbcType=VARCHAR},
      </if>
      <if test="oldSex != null">
        oldSex = #{oldSex,jdbcType=VARCHAR},
      </if>
      <if test="oldBirthday != null">
        oldBirthday = #{oldBirthday,jdbcType=VARCHAR},
      </if>
      <if test="newName != null">
        newName = #{newName,jdbcType=VARCHAR},
      </if>
      <if test="newNativeplace != null">
        newNativeplace = #{newNativeplace,jdbcType=VARCHAR},
      </if>
      <if test="newIdType != null">
        newIdType = #{newIdType,jdbcType=VARCHAR},
      </if>
      <if test="newIdNo != null">
        newIdNo = #{newIdNo,jdbcType=VARCHAR},
      </if>
      <if test="newIdTypeEndDate != null">
        newIdTypeEndDate = #{newIdTypeEndDate,jdbcType=DATE},
      </if>
      <if test="newSex != null">
        newSex = #{newSex,jdbcType=VARCHAR},
      </if>
      <if test="newBirthday != null">
        newBirthday = #{newBirthday,jdbcType=VARCHAR},
      </if>
      <if test="newMobilePhone != null">
        newMobilePhone = #{newMobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="newJobType != null">
        newJobType = #{newJobType,jdbcType=VARCHAR},
      </if>
      <if test="newJobCode != null">
        newJobCode = #{newJobCode,jdbcType=VARCHAR},
      </if>
      <if test="newJoinMedProtect != null">
        newJoinMedProtect = #{newJoinMedProtect,jdbcType=VARCHAR},
      </if>
      <if test="nzValidate != null">
          nzValidate = #{nzValidate,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where HomogeneousRiskInsuredSN = #{homogeneousRiskInsuredSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured">
    update fcedorhomogeneousriskinsured
    set GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      Batch = #{batch,jdbcType=VARCHAR},
      GrpNo = #{grpNo,jdbcType=VARCHAR},
      oldName = #{oldName,jdbcType=VARCHAR},
      oldIdType = #{oldIdType,jdbcType=VARCHAR},
      oldIdNo = #{oldIdNo,jdbcType=VARCHAR},
      oldSex = #{oldSex,jdbcType=VARCHAR},
      oldBirthday = #{oldBirthday,jdbcType=VARCHAR},
      newName = #{newName,jdbcType=VARCHAR},
      newNativeplace = #{newNativeplace,jdbcType=VARCHAR},
      newIdType = #{newIdType,jdbcType=VARCHAR},
      newIdNo = #{newIdNo,jdbcType=VARCHAR},
      newIdTypeEndDate = #{newIdTypeEndDate,jdbcType=DATE},
      newSex = #{newSex,jdbcType=VARCHAR},
      newBirthday = #{newBirthday,jdbcType=VARCHAR},
      newMobilePhone = #{newMobilePhone,jdbcType=VARCHAR},
      newJobType = #{newJobType,jdbcType=VARCHAR},
      newJobCode = #{newJobCode,jdbcType=VARCHAR},
      newJoinMedProtect = #{newJoinMedProtect,jdbcType=VARCHAR},
      nzValidate = #{nzValidate,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where HomogeneousRiskInsuredSN = #{homogeneousRiskInsuredSN,jdbcType=VARCHAR}
  </update>
  
  <select id="selectInsuredCount" parameterType="java.util.Map" resultType="int">
    select COUNT(*) from fcedorhomogeneousriskinsured c where c.GrpNo = #{grpNo,jdbcType=VARCHAR}
    and c.Batch=#{batch,jdbcType=VARCHAR}
  </select>
  <delete id="deleteHomogeneousRiskInsured"  parameterType="java.lang.String">
  		delete from fcedorhomogeneousriskinsured  where batch = #{batch,jdbcType=VARCHAR}
  </delete>
  <!-- 批量插入同质风险加减人 -->
  <insert id="insertNext" parameterType="java.util.List">      
   	  insert into fcedorhomogeneousriskinsured (HomogeneousRiskInsuredSN, GrpContNo, 
	      Batch, GrpNo, oldName, 
	      oldIdType, oldIdNo, oldSex, 
	      oldBirthday, newName, newNativeplace, 
	      newIdType, newIdNo, newIdTypeEndDate, 
	      newSex, newBirthday, newMobilePhone, 
	      newJobType, newJobCode, newJoinMedProtect, nzValidate,
	      Operator, OperatorCom, MakeDate, 
	      MakeTime, ModifyDate, ModifyTime)
      <foreach collection="list" index="index" item="item" separator="union all">
      	select
	      #{list[${index}].homogeneousRiskInsuredSN,jdbcType=VARCHAR},#{list[${index}].grpContNo,jdbcType=VARCHAR}, 
	      #{list[${index}].batch,jdbcType=VARCHAR}, #{list[${index}].grpNo,jdbcType=VARCHAR}, #{list[${index}].oldName,jdbcType=VARCHAR}, 
	      #{list[${index}].oldIdType,jdbcType=VARCHAR}, #{list[${index}].oldIdNo,jdbcType=VARCHAR}, #{list[${index}].oldSex,jdbcType=VARCHAR}, 
	      #{list[${index}].oldBirthday,jdbcType=VARCHAR}, #{list[${index}].newName,jdbcType=VARCHAR}, #{list[${index}].newNativeplace,jdbcType=VARCHAR}, 
	      #{list[${index}].newIdType,jdbcType=VARCHAR}, #{list[${index}].newIdNo,jdbcType=VARCHAR}, #{list[${index}].newIdTypeEndDate,jdbcType=DATE}, 
	      #{list[${index}].newSex,jdbcType=VARCHAR}, #{list[${index}].newBirthday,jdbcType=VARCHAR}, #{list[${index}].newMobilePhone,jdbcType=VARCHAR}, 
	      #{list[${index}].newJobType,jdbcType=VARCHAR}, #{list[${index}].newJobCode,jdbcType=VARCHAR}, #{list[${index}].newJoinMedProtect,jdbcType=VARCHAR},
          #{list[${index}].nzValidate,jdbcType=VARCHAR},
          #{list[${index}].operator,jdbcType=VARCHAR}, #{list[${index}].operatorCom,jdbcType=VARCHAR}, #{list[${index}].makeDate,jdbcType=DATE},
	      #{list[${index}].makeTime,jdbcType=VARCHAR}, #{list[${index}].modifyDate,jdbcType=DATE}, #{list[${index}].modifyTime,jdbcType=VARCHAR}
      	from dual
      </foreach>
  </insert>
  
  <select id="getHomogeneousRiskInsuredInfo" resultType="com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured">
    select
    <include refid="Base_Column_List" />
    from FCEdorHomogeneousRiskInsured where batch=#{batch,jdbcType=VARCHAR} and GrpNo = #{grpNo,jdbcType=VARCHAR}
  </select>
    <select id="selectEdorHomogeneousRiskInsured"
            parameterType="com.sinosoft.eflex.model.edor.SelectEdorHomogeneousRiskInsuredReq"
            resultType="com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsuredInfo">
    select
        a.HomogeneousRiskInsuredSN, a.GrpContNo, a.Batch, a.GrpNo,
        a.oldName,
       a.oldIdType , b.CodeName oldIdTypeName,
       a.oldIdNo,
       a.oldSex, c.CodeName oldSexName,
        a.oldBirthday,
        a.newName,
       a.newNativeplace, d.CodeName newNativeplaceName,
       a.newIdType , e.CodeName newIdTypeName,
       a.newIdNo, a.newIdTypeEndDate,
       a.newSex, f.CodeName newSexName,
       a.newBirthday, a.newMobilePhone, a.newJobType,
       a.newJobCode,h.CodeName newJobCodeName,
       a.newJoinMedProtect,g.CodeName newJoinMedProtectName,
       a.nzValidate,a.Operator,
       a.OperatorCom, a.MakeDate, a.MakeTime, a.ModifyDate, a.ModifyTime
    from FCEdorHomogeneousRiskInsured a
    left JOIN fdcode b on a.oldIdType = b.CodeKey and b.CodeType='IDType'
    left JOIN fdcode c on a.oldSex = c.CodeKey and c.CodeType='sex'
    left JOIN fdcode d on a.newNativeplace = d.CodeKey and d.CodeType='Nativeplace'
    left JOIN fdcode e on a.newIdType = e.CodeKey and e.CodeType='IDType'
    left JOIN fdcode f on a.newSex = f.CodeKey and f.CodeType='sex'
    left JOIN fdcode g on a.newJoinMedProtect = g.CodeKey and g.CodeType='SociologyPlanSign'
    left JOIN fdcode h on a.newJobCode = h.CodeKey and h.CodeType='OccupationDetail'
        where a.batch = #{hrarBatch}
        and a.GrpNo = #{grpNo}
        <if test="oldName != null and oldName != '' ">
            and a.oldName like concat('%',#{oldName},'%')
        </if>
        <if test="oldIdType != null and oldIdType != '' ">
            and a.oldIdType = #{oldIdType}
        </if>
        <if test="oldIdNo != null and oldIdNo != '' ">
            and a.oldIdNo = #{oldIdNo}
        </if>
        <if test="newName != null and newName != '' ">
            and a.newName like concat('%',#{newName},'%')
        </if>
        <if test="newIdType != null and newIdType != '' ">
            and a.newIdType = #{newIdType}
        </if>
        <if test="newIdNo != null and newIdNo != '' ">
            and a.newIdNo = #{newIdNo}
        </if>
  </select>
  
</mapper>