<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcBusinessProDutyGrpObjectMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcBusinessProDutyGrpObject">
    <id column="SerialNo" jdbcType="VARCHAR" property="serialNo" />
    <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="DutyName" jdbcType="VARCHAR" property="dutyName" />
    <result column="GradeLevelTopLimit" jdbcType="VARCHAR" property="gradeLevelTopLimit" />
    <result column="GradeLevelLowLimit" jdbcType="VARCHAR" property="gradeLevelLowLimit" />
    <result column="GradeLevelTopLimitName" jdbcType="VARCHAR" property="gradeLevelTopLimitName" />
    <result column="GradeLevelLowLimitName" jdbcType="VARCHAR" property="gradeLevelLowLimitName" />
    <result column="OccupationTypeTopLimit" jdbcType="VARCHAR" property="occupationTypeTopLimit" />
    <result column="OccupationTypeLowLimit" jdbcType="VARCHAR" property="occupationTypeLowLimit" />
    <result column="AgeTopLimit" jdbcType="VARCHAR" property="ageTopLimit" />
    <result column="AgeLowLimit" jdbcType="VARCHAR" property="ageLowLimit" />
    <result column="ComeAgeTopLimit" jdbcType="VARCHAR" property="comeAgeTopLimit" />
    <result column="ComeAgeLowLimit" jdbcType="VARCHAR" property="comeAgeLowLimit" />
    <result column="Sex" jdbcType="VARCHAR" property="sex" />
    <result column="Retirement" jdbcType="VARCHAR" property="retirement" />
    <result column="DefaultDeductible" jdbcType="DOUBLE" property="defaultDeductible" />
    <result column="DefaultCompensationRatio" jdbcType="DOUBLE" property="defaultCompensationRatio" />
    <result column="IsDefaultFlag" jdbcType="VARCHAR" property="isDefaultFlag" />
    <result column="Amnt" jdbcType="DOUBLE" property="amnt" />
    <result column="Prem" jdbcType="DOUBLE" property="prem" />
    <result column="InsuredType" jdbcType="VARCHAR" property="insuredType" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <collection property="fcDutyAmountGradeList" resultMap="FcDutyAmountGradeBaseResultMap"/>
  </resultMap>

  <resultMap id="FcDutyAmountGradeBaseResultMap" type="com.sinosoft.eflex.model.FcDutyAmountGrade">
    <id column="grageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <result column="RiskType" jdbcType="VARCHAR" property="riskType" />
    <result column="DutyCode" jdbcType="VARCHAR" property="dutyCode" />
    <result column="AmountGrageName" jdbcType="VARCHAR" property="amountGrageName" />
    <result column="Amnt" jdbcType="DOUBLE" property="amnt" />
    <result column="riskName" jdbcType="VARCHAR" property="riskName" />
    <collection property="fcDutyGroupDeductibleList" ofType="java.lang.String">
      <constructor>
        <arg column="deductible"/>
      </constructor>
    </collection>
    <collection property="fcDutGradeCompensationRatioList" ofType="java.lang.String">
      <constructor>
        <arg column="compensationRatio"/>
      </constructor>
    </collection>
  </resultMap>

  <sql id="Base_Column_List">
    SerialNo, EnsureCode, AmountGrageCode, GrpNo, GradeLevelTopLimit, GradeLevelLowLimit, 
    OccupationTypeTopLimit, OccupationTypeLowLimit, AgeTopLimit, AgeLowLimit, ComeAgeTopLimit, 
    ComeAgeLowLimit, Sex, Retirement, DefaultDeductible, DefaultCompensationRatio, IsDefaultFlag, 
    Amnt, Prem, InsuredType, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultType="com.sinosoft.eflex.model.FcBusinessProDutyGrpObject">
    select 
    <include refid="Base_Column_List" />
    from fcbusinessprodutygrpobject
    where 1=1
    <if test=" serialNo != null serialNo != '' ">
      SerialNo = #{serialNo,jdbcType=VARCHAR}
    </if>
    <if test=" ensureCode != null ensureCode != '' ">
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </if>
    <if test=" AmountGrageCode != null amountGrageCode != '' ">
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="selectByEnsureCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcBusinessProDutyGrpObject">
    select
    <include refid="Base_Column_List" />,
    (SELECT GradeLevelCode FROM FCBusPersonType WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND OrderNum = `GradeLevelLowLimit`) AS gradeLevelLowLimitName,
    (SELECT GradeLevelCode FROM FCBusPersonType WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND OrderNum = `GradeLevelTopLimit`) AS gradeLevelTopLimitName
    from fcbusinessprodutygrpobject
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
     <if test=' isCheck == "01" '>
       AND InsuredType = '0'
     </if>
    <if test=' isCheck != "01" and isCheck != null and isCheck != "" '>
      AND InsuredType not in ('0')
    </if>
    GROUP by SerialNo
  </select>
 <select id="selectByGrpNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcBusinessProDutyGrpObject">
    select
    <include refid="Base_Column_List" />,
    (SELECT GradeLevelCode FROM FCBusPersonType WHERE GrpNo = #{grpNo,jdbcType=VARCHAR} AND OrderNum = `GradeLevelLowLimit`) AS gradeLevelLowLimitName,
    (SELECT GradeLevelCode FROM FCBusPersonType WHERE GrpNo = #{grpNo,jdbcType=VARCHAR} AND OrderNum = `GradeLevelTopLimit`) AS gradeLevelTopLimitName
    from fcbusinessprodutygrpobject
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
     <if test=' isCheck == "01" '>
       AND InsuredType = '0'
     </if>
    <if test=' isCheck != "01" and isCheck != null and isCheck != "" '>
      AND InsuredType not in ('0')
    </if>
    GROUP by SerialNo
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.util.Map">
    delete from fcbusinessprodutygrpobject
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
      <if test=" ensureCode != null and ensureCode != ''">
        and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      </if>
      <if test=" amountGrageCode != null and amountGrageCode != ''">
        and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      </if>
  </delete>

  <delete id="deleteByEnsureCode" parameterType="java.lang.String">
    delete from fcbusinessprodutygrpobject
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    <if test=' insuredType == "0" '>
      AND InsuredType = '0'
    </if>
    <if test=' insuredType != "0" '>
      AND InsuredType != '0'
    </if>
  </delete>

  <insert id="insertList" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="" close=""  separator=";">
    insert into fcbusinessprodutygrpobject (SerialNo, EnsureCode, AmountGrageCode, 
      GrpNo, GradeLevelTopLimit, GradeLevelLowLimit, 
      OccupationTypeTopLimit, OccupationTypeLowLimit, 
      AgeTopLimit, AgeLowLimit, ComeAgeTopLimit, 
      ComeAgeLowLimit, Sex, Retirement,
      <if test="item.defaultDeductible != null">
        DefaultDeductible,
      </if>
      <if test="item.defaultCompensationRatio != null">
        DefaultCompensationRatio,
      </if>
      IsDefaultFlag,
      <if test="item.amnt != null and item.amnt != ''">
        Amnt,
      </if>
      <if test="item.prem != null and item.prem != ''">
        Prem,
      </if>
      InsuredType, Operator, OperatorCom,
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values(#{list[${index}].serialNo,jdbcType=VARCHAR},
      #{list[${index}].ensureCode,jdbcType=VARCHAR},
      #{list[${index}].amountGrageCode,jdbcType=VARCHAR},
      #{list[${index}].grpNo,jdbcType=VARCHAR},
      #{list[${index}].gradeLevelTopLimit,jdbcType=VARCHAR},
      #{list[${index}].gradeLevelLowLimit,jdbcType=VARCHAR},
      #{list[${index}].occupationTypeTopLimit,jdbcType=VARCHAR},
      #{list[${index}].occupationTypeLowLimit,jdbcType=VARCHAR},
      #{list[${index}].ageTopLimit,jdbcType=VARCHAR},
      #{list[${index}].ageLowLimit,jdbcType=VARCHAR},
      #{list[${index}].comeAgeTopLimit,jdbcType=VARCHAR},
      #{list[${index}].comeAgeLowLimit,jdbcType=VARCHAR},
      #{list[${index}].sex,jdbcType=VARCHAR},
      #{list[${index}].retirement,jdbcType=VARCHAR},
      <if test="item.defaultDeductible != null">
        #{list[${index}].defaultDeductible,jdbcType=DOUBLE},
      </if>
      <if test="item.defaultCompensationRatio != null">
        #{list[${index}].defaultCompensationRatio,jdbcType=DOUBLE},
      </if>
      #{list[${index}].isDefaultFlag,jdbcType=VARCHAR},
      <if test="item.amnt != null and item.amnt != ''">
        #{list[${index}].amnt,jdbcType=DOUBLE},
      </if>
      <if test="item.prem != null and item.prem != ''">
        #{list[${index}].prem,jdbcType=DOUBLE},
      </if>
      #{list[${index}].insuredType,jdbcType=VARCHAR},
      #{list[${index}].operator,jdbcType=VARCHAR},
      #{list[${index}].operatorCom,jdbcType=VARCHAR},
      #{list[${index}].makeDate,jdbcType=DATE},
      #{list[${index}].makeTime,jdbcType=VARCHAR},
      #{list[${index}].modifyDate,jdbcType=DATE},
      #{list[${index}].modifyTime,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcBusinessProDutyGrpObject">
    insert into fcbusinessprodutygrpobject
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        SerialNo,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="amountGrageCode != null">
        AmountGrageCode,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="gradeLevelTopLimit != null">
        GradeLevelTopLimit,
      </if>
      <if test="gradeLevelLowLimit != null">
        GradeLevelLowLimit,
      </if>
      <if test="occupationTypeTopLimit != null">
        OccupationTypeTopLimit,
      </if>
      <if test="occupationTypeLowLimit != null">
        OccupationTypeLowLimit,
      </if>
      <if test="ageTopLimit != null">
        AgeTopLimit,
      </if>
      <if test="ageLowLimit != null">
        AgeLowLimit,
      </if>
      <if test="comeAgeTopLimit != null">
        ComeAgeTopLimit,
      </if>
      <if test="comeAgeLowLimit != null">
        ComeAgeLowLimit,
      </if>
      <if test="sex != null">
        Sex,
      </if>
      <if test="retirement != null">
        Retirement,
      </if>
      <if test="defaultDeductible != null">
        DefaultDeductible,
      </if>
      <if test="defaultCompensationRatio != null">
        DefaultCompensationRatio,
      </if>
      <if test="isDefaultFlag != null">
        IsDefaultFlag,
      </if>
      <if test="amnt != null">
        Amnt,
      </if>
      <if test="prem != null">
        Prem,
      </if>
      <if test="insuredType != null">
        InsuredType,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="amountGrageCode != null">
        #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="gradeLevelTopLimit != null">
        #{gradeLevelTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="gradeLevelLowLimit != null">
        #{gradeLevelLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="occupationTypeTopLimit != null">
        #{occupationTypeTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="occupationTypeLowLimit != null">
        #{occupationTypeLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="ageTopLimit != null">
        #{ageTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="ageLowLimit != null">
        #{ageLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="comeAgeTopLimit != null">
        #{comeAgeTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="comeAgeLowLimit != null">
        #{comeAgeLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="retirement != null">
        #{retirement,jdbcType=VARCHAR},
      </if>
      <if test="defaultDeductible != null">
        #{defaultDeductible,jdbcType=DOUBLE},
      </if>
      <if test="defaultCompensationRatio != null">
        #{defaultCompensationRatio,jdbcType=DOUBLE},
      </if>
      <if test="isDefaultFlag != null">
        #{isDefaultFlag,jdbcType=VARCHAR},
      </if>
      <if test="amnt != null">
        #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="prem != null">
        #{prem,jdbcType=DOUBLE},
      </if>
      <if test="insuredType != null">
        #{insuredType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcBusinessProDutyGrpObject">
    update fcbusinessprodutygrpobject
    <set>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="gradeLevelTopLimit != null">
        GradeLevelTopLimit = #{gradeLevelTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="gradeLevelLowLimit != null">
        GradeLevelLowLimit = #{gradeLevelLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="occupationTypeTopLimit != null">
        OccupationTypeTopLimit = #{occupationTypeTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="occupationTypeLowLimit != null">
        OccupationTypeLowLimit = #{occupationTypeLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="ageTopLimit != null">
        AgeTopLimit = #{ageTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="ageLowLimit != null">
        AgeLowLimit = #{ageLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="comeAgeTopLimit != null">
        ComeAgeTopLimit = #{comeAgeTopLimit,jdbcType=VARCHAR},
      </if>
      <if test="comeAgeLowLimit != null">
        ComeAgeLowLimit = #{comeAgeLowLimit,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        Sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="retirement != null">
        Retirement = #{retirement,jdbcType=VARCHAR},
      </if>
      <if test="defaultDeductible != null">
        DefaultDeductible = #{defaultDeductible,jdbcType=DOUBLE},
      </if>
      <if test="defaultCompensationRatio != null">
        DefaultCompensationRatio = #{defaultCompensationRatio,jdbcType=DOUBLE},
      </if>
      <if test="isDefaultFlag != null">
        IsDefaultFlag = #{isDefaultFlag,jdbcType=VARCHAR},
      </if>
      <if test="amnt != null">
        Amnt = #{amnt,jdbcType=DOUBLE},
      </if>
      <if test="prem != null">
        Prem = #{prem,jdbcType=DOUBLE},
      </if>
      <if test="insuredType != null">
        InsuredType = #{insuredType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcBusinessProDutyGrpObject">
    update fcbusinessprodutygrpobject
    set GrpNo = #{grpNo,jdbcType=VARCHAR},
      GradeLevelTopLimit = #{gradeLevelTopLimit,jdbcType=VARCHAR},
      GradeLevelLowLimit = #{gradeLevelLowLimit,jdbcType=VARCHAR},
      OccupationTypeTopLimit = #{occupationTypeTopLimit,jdbcType=VARCHAR},
      OccupationTypeLowLimit = #{occupationTypeLowLimit,jdbcType=VARCHAR},
      AgeTopLimit = #{ageTopLimit,jdbcType=VARCHAR},
      AgeLowLimit = #{ageLowLimit,jdbcType=VARCHAR},
      ComeAgeTopLimit = #{comeAgeTopLimit,jdbcType=VARCHAR},
      ComeAgeLowLimit = #{comeAgeLowLimit,jdbcType=VARCHAR},
      Sex = #{sex,jdbcType=VARCHAR},
      Retirement = #{retirement,jdbcType=VARCHAR},
      DefaultDeductible = #{defaultDeductible,jdbcType=DOUBLE},
      DefaultCompensationRatio = #{defaultCompensationRatio,jdbcType=DOUBLE},
      IsDefaultFlag = #{isDefaultFlag,jdbcType=VARCHAR},
      Amnt = #{amnt,jdbcType=DOUBLE},
      Prem = #{prem,jdbcType=DOUBLE},
      InsuredType = #{insuredType,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
      and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
  </update>
  
  <select id="selectDefaultRiskGrade" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FcDefaultRiskGrade">
  	select 
		a.AmountGrageCode,
		b.RiskCode,
		b.RiskType,
		CASE WHEN b.RiskCode='15070' then 
								CASE WHEN b.RiskType='01' then CONCAT(c.RiskName,'(民航班机)')
									 WHEN b.RiskType='02' then CONCAT(c.RiskName,'(轨道交通工具)')
								     WHEN b.RiskType='03' then CONCAT(c.RiskName,'(水运公共交通工具)')
									 WHEN b.RiskType='04' then CONCAT(c.RiskName,'(公路公共交通工具)')
								else CONCAT(c.RiskName,'(私家车)') end
					 else c.RiskName end as RiskName,
		b.Amnt,
        if(b.RiskCode='15070','',a.DefaultCompensationRatio) CompensationRatio,
        if(b.RiskCode='15070','',a.DefaultDeductible) Deductible,
        if(b.RiskCode='15070','',b.AnnualTimeDeduction) DeductibleAttr
		from  
		FcBusinessProDutyGrpObject a,
		FcDutyAmountGrade b,
		fdriskinfo c
		where a.EnsureCode=#{EnsureCode}
		and a.AmountGrageCode=b.AmountGrageCode 
		and b.RiskCode=c.RiskCode
		<!-- 职级 -->
		<if test="LevelCod != null">
			and  (#{LevelCod}  between a.GradeLevelTopLimit and a.GradeLevelLowLimit OR a.GradeLevelTopLimit = '' OR a.GradeLevelTopLimit is NULL)
		</if>
		<!-- 职业类别 -->
			and (#{OccupationType} between a.OccupationTypeLowLimit and a.OccupationTypeTopLimit OR a.OccupationTypeLowLimit = '' OR a.OccupationTypeLowLimit is NULL)
		<!-- 投保年龄 -->
			and (TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) between a.AgeLowLimit and a.AgeTopLimit OR a.AgeLowLimit = '' OR a.AgeLowLimit is NULL )
		<!-- 服务年限 -->
		<if test="ServiceTerm != null">
			and (#{ServiceTerm}+0 between a.ComeAgeLowLimit and a.ComeAgeTopLimit or a.ComeAgeLowLimit = '' or  a.ComeAgeLowLimit is NULL )
		</if>
		<!-- 是否退休 -->
		<if test="Retirement != null">
			and (#{Retirement} = a.Retirement or a.Retirement='2' or a.Retirement = '' OR  a.Retirement is NULL )
		</if>
		<!-- 性别 -->
		and (#{Sex} = a.Sex or a.sex='2' or a.`Sex` = '' OR a.`Sex` is NULL )
        <if test=' Relation == "0" '>
          and #{Relation} = a.InsuredType
        </if>
        <if test=' Relation != "0" '>
          and (#{Relation} in ((SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(a.InsuredType, '@', seq),'@' ,-1) sub_id
          FROM sequence WHERE seq BETWEEN 1 AND (SELECT 1 + LENGTH(a.InsuredType) - LENGTH(replace(a.InsuredType, '@', '')))
          )) or a.InsuredType = '' OR  a.InsuredType is NULL )
        </if>
		and a.IsDefaultFlag='1'
  </select>

  <select id="getFcDutyAmountGradeListByEnsureCode" parameterType="java.util.Map" resultMap="com.sinosoft.eflex.dao.FcDutyAmountGradeMapper.BaseResultMap">
    SELECT a.*,b.Deductible  AS deductible,d.CompensationRatio AS compensationRatio,'0' as isDefaultFlag,r.`RiskName` AS RiskName,
    IF((SELECT AmountGrageCode FROM FcBusinessProDutyGrpObject WHERE EnsureCode = #{ensureCode}
    AND SerialNo = #{serialNo} AND AmountGrageCode = a.AmountGrageCode) IS NULL ,'0','1') AS existAmountGrageCode
    FROM FcDutyAmountGrade a
    RIGHT JOIN FcPlanRiskInfo e ON e.RiskCode = a.RiskCode AND e.RiskType = a.RiskType AND e.EnsureCode = a.EnsureCode
    LEFT JOIN FcDutyGroupDeductible b ON a.AmountGrageCode = b.AmountGrageCode AND b.AmountGrageCode = (select case RiskCode WHEN '15070' THEN '不显示' ELSE AmountGrageCode  END AS amountGrageCode from  FcDutyAmountGrade where AmountGrageCode = a.AmountGrageCode)
    LEFT JOIN FcDutGradeCompensationRatio d ON a.AmountGrageCode = d.AmountGrageCode AND d.AmountGrageCode = (select case RiskCode WHEN '15070' THEN '不显示' ELSE AmountGrageCode  END AS amountGrageCode from  FcDutyAmountGrade where AmountGrageCode = a.AmountGrageCode)
    LEFT JOIN fdriskinfo r ON r.RiskCode = a.RiskCode
    WHERE a.EnsureCode = #{ensureCode}
    ORDER by a.RiskCode,b.Deductible,d.CompensationRatio ASC
  </select>

  <select id="getDutyAmountGradeBySerialNo" parameterType="java.util.Map" resultMap="BaseResultMap">
    select b.*,d.RiskCode as riskCode,d.AmountGrageName as amountGrageName,d.Amnt as amnt,r.RiskName as riskName,d.AmountGrageCode as grageCode,d.DutyName as dutyName,
    e.Deductible as deductible,g.CompensationRatio as compensationRatio,
    (SELECT GradeLevelCode FROM FCBusPersonType WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND OrderNum = b.GradeLevelLowLimit) AS gradeLevelLowLimitName,
    (SELECT GradeLevelCode FROM FCBusPersonType WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND OrderNum = b.GradeLevelTopLimit) AS gradeLevelTopLimitName
     from  FcBusinessProDutyGrpObject b
     left join  FcDutyAmountGrade d  on b.AmountGrageCode = d.AmountGrageCode
     left join fdriskInfo r on   d.riskCode = r.riskCode
     left join FcDutyGroupDeductible e on e.AmountGrageCode = d.AmountGrageCode AND e.AmountGrageCode = (select case RiskCode WHEN '15070' THEN '不显示' ELSE AmountGrageCode  END AS amountGrageCode from  FcDutyAmountGrade where AmountGrageCode = d.AmountGrageCode)
     left join FcDutGradeCompensationRatio g on g.AmountGrageCode = d.AmountGrageCode AND g.AmountGrageCode = (select case RiskCode WHEN '15070' THEN '不显示' ELSE AmountGrageCode  END AS amountGrageCode from  FcDutyAmountGrade where AmountGrageCode = d.AmountGrageCode)
     where serialNo = #{serialNo}
     ORDER by e.Deductible,g.CompensationRatio ASC
  </select>

    <select id="getAllDutyAmountGradeBySerialNo" parameterType="java.util.Map" resultMap="com.sinosoft.eflex.dao.FcDutyAmountGradeMapper.BaseResultMap">
      SELECT a.*,r.`RiskName` AS RiskName,a.AmountGrageCode AS grageCode,a.`AmountGrageCode` AS amountGrageCode,a.AmountGrageName AS amountGrageName,
      CAST(b.`DefaultDeductible` AS SIGNED) AS defaultDeductible,CAST(b.`DefaultCompensationRatio` AS SIGNED) AS defaultCompensationRatio,if(b.`IsDefaultFlag` is NULL,'0',b.`IsDefaultFlag`) AS isDefaultFlag,
      e.Deductible AS deductible,g.CompensationRatio AS compensationRatio,
      if(b.`IsDefaultFlag` is NULL,'0','1') AS existAmountGrageCode
      FROM FcDutyAmountGrade a
      LEFT JOIN FcBusinessProDutyGrpObject b ON b.`AmountGrageCode` = a.`AmountGrageCode` AND b.`SerialNo` = #{serialNo}
      RIGHT JOIN FcPlanRiskInfo c ON c.RiskCode = a.RiskCode AND c.RiskType = a.RiskType AND c.EnsureCode = a.EnsureCode
      LEFT JOIN fdriskInfo r ON a.`RiskCode` = r.`RiskCode`
      LEFT JOIN FcDutyGroupDeductible e ON e.AmountGrageCode = a.AmountGrageCode AND e.AmountGrageCode = (SELECT CASE RiskCode WHEN '15070' THEN '不显示' ELSE AmountGrageCode  END AS amountGrageCode FROM  FcDutyAmountGrade WHERE AmountGrageCode = a.AmountGrageCode)
      LEFT JOIN FcDutGradeCompensationRatio g ON g.AmountGrageCode = a.AmountGrageCode  AND g.AmountGrageCode = (select case RiskCode WHEN '15070' THEN '不显示' ELSE AmountGrageCode  END AS amountGrageCode from  FcDutyAmountGrade where AmountGrageCode = a.AmountGrageCode)
      WHERE a.`EnsureCode` = #{ensureCode,jdbcType=VARCHAR}
      ORDER BY b.`IsDefaultFlag` DESC,a.RiskCode,e.Deductible,g.CompensationRatio ASC
  </select>
    <select id="selectInsuredType" parameterType="java.lang.String" resultType="java.lang.String">
        select insuredType from FcBusinessProDutyGrpObject
        where EnsureCode = #{EnsureCode}
          and insuredType is not null
          and insuredType != ''
    </select>
</mapper>