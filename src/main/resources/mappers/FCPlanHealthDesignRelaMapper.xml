<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPlanHealthDesignRelaMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
        <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <id column="DesignNo" jdbcType="VARCHAR" property="designNo"/>
        <result column="DesignStatus" jdbcType="VARCHAR" property="designStatus"/>
        <result column="returnReason" jdbcType="VARCHAR" property="returnReason"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    EnsureCode, DesignNo, DesignStatus,ReturnReason,Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>

    <select id="selectByEnsureCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
      SELECT DesignNo,DesignStatus,returnReason FROM fcplanhealthdesignrela WHERE EnsureCode = #{ensureCode}
    </select>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcplanhealthdesignrela
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and DesignNo = #{designNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fcplanhealthdesignrela
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and DesignNo = #{designNo,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
    insert into fcplanhealthdesignrela (EnsureCode, DesignNo, DesignStatus,ReturnReason,Operator,
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{ensureCode,jdbcType=VARCHAR}, #{designNo,jdbcType=VARCHAR}, #{designStatus,jdbcType=VARCHAR},
        #{returnReason,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
        insert into fcplanhealthdesignrela
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="designNo != null">
                DesignNo,
            </if>
            <if test="designStatus != null">
                DesignStatus,
            </if>
            <if test="returnReason != null">
                ReturnReason,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="designNo != null">
                #{designNo,jdbcType=VARCHAR},
            </if>
            <if test="designStatus != null">
                #{designStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnReason != null">
                #{returnReason,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
        update fcplanhealthdesignrela
        <set>
            <if test="designStatus != null">
                DesignStatus = #{designStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnReason != null">
                ReturnReason = #{returnReason,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and DesignNo = #{designNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
    update fcplanhealthdesignrela
    set DesignStatus = #{designStatus,jdbcType=VARCHAR},
        ReturnReason = #{returnReason,jdbcType=VARCHAR},
        Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and DesignNo = #{designNo,jdbcType=VARCHAR}
  </update>
    <update id="updateByEnsureCode" parameterType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
        update fcplanhealthdesignrela
        <set>
            <if test="designStatus != null">
                DesignStatus = #{designStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnReason != null">
                ReturnReason = #{returnReason,jdbcType=VARCHAR},
            </if>
            <if test="designNo != null">
                DesignNo = #{designNo,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByDesignNo" parameterType="com.sinosoft.eflex.model.FCPlanHealthDesignRela">
        update fcplanhealthdesignrela
        <set>
            <if test="ensureCode != null">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="designStatus != null">
                DesignStatus = #{designStatus,jdbcType=VARCHAR},
            </if>
            <if test="returnReason != null">
                ReturnReason = #{returnReason,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where DesignNo = #{designNo,jdbcType=VARCHAR}
    </update>
  <select id="checkIsNeedInform" resultType="java.lang.Integer" parameterType="java.util.Map">
	  	select count(1) 
	  	from FCPlanHealthDesignRela a,
		  	 FCHealthDesignDetailRela b,
		  	 FCHealthDesignDetail c
		where a.DesignNo=b.DesignNo
		and b.HealthDesignNo=c.HealthDesignNo
		and a.ensureCode=#{ensureCode,jdbcType=VARCHAR}
		<if test="LevelCode != null and LevelCode !=''">
		and (#{LevelCode} 
		BETWEEN c.GradeLevelTopLimit
		and c.GradeLevelLowLimit
			  or (c.GradeLevelTopLimit is null and c.GradeLevelLowLimit is null)
			  or (c.GradeLevelTopLimit ='' and c.GradeLevelLowLimit =''))
		</if>
		and (#{Amnt,jdbcType=DOUBLE}
		BETWEEN c.AmntLowLimit AND c.AmntTopLimit or (c.AmntLowLimit IS NULL AND c.AmntTopLimit IS NULL))
	    and (TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) BETWEEN c.AgeLowLimit AND c.AgeTopLimit
	    or (c.AgeLowLimit IS NULL AND c.AgeTopLimit IS NULL)
	    or (c.AgeLowLimit='' and c.AgeTopLimit=''))
	    and (c.sex=#{Sex,jdbcType=VARCHAR} or c.sex is null or c.sex='')
	    and (c.InsuredType=#{relation,jdbcType=VARCHAR} 
	    or (c.InsuredType='1' and #{relation,jdbcType=VARCHAR}&lt;&gt;'0') 
	    or c.InsuredType IS NULL
	    or c.InsuredType = '')
	    and c.riskCode=#{RiskCode,jdbcType=VARCHAR} 
  </select>
    <select id="checkIsNeedHealthNotice"
            parameterType="com.sinosoft.eflex.model.confirmInsureEflex.CheckIsNeedInformReq"
            resultType="java.lang.Integer">
        select
        count(1)
        from FCPlanHealthDesignRela a,
        FCHealthDesignDetailRela b,
        FCHealthDesignDetail c
        where a.DesignNo=b.DesignNo
        and b.HealthDesignNo=c.HealthDesignNo
        <!-- 福利编码 -->
        and a.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        <!-- 职级 -->
        <if test="levelCode != null and levelCode !=''">
            and (#{levelCode} BETWEEN c.GradeLevelTopLimit and c.GradeLevelLowLimit
            or (c.GradeLevelTopLimit is null and c.GradeLevelLowLimit is null)
            or (c.GradeLevelTopLimit ='' and c.GradeLevelLowLimit =''))
        </if>
        <!-- 保额 -->
        and (#{amount,jdbcType=DOUBLE} BETWEEN c.AmntLowLimit AND c.AmntTopLimit or (c.AmntLowLimit IS NULL AND
        c.AmntTopLimit IS NULL))
        <!-- 根据出生日期，判断年龄上下限 -->
        and (TIMESTAMPDIFF(YEAR, #{birthDay}, #{cvaliDate}) BETWEEN c.AgeLowLimit AND c.AgeTopLimit
        or (c.AgeLowLimit IS NULL AND c.AgeTopLimit IS NULL)
        or (c.AgeLowLimit='' and c.AgeTopLimit=''))
        <!-- 性别 -->
        and (c.sex=#{sex,jdbcType=VARCHAR} or c.sex is null or c.sex='')
        <!-- 与员工关系 -->
        and (c.InsuredType=#{relation,jdbcType=VARCHAR}
        or (c.InsuredType='1' and #{relation,jdbcType=VARCHAR}&lt;&gt;'0')
        or c.InsuredType IS NULL
        or c.InsuredType = '')
        <!-- 险种编码 -->
        and c.riskCode=#{riskCode,jdbcType=VARCHAR}
    </select>
</mapper>