<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDRiskCompensationRatioMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDRiskCompensationRatio">
    <id column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <id column="CompensationRatio" jdbcType="DOUBLE" property="compensationRatio" />
    <result column="adjustfactor" jdbcType="DOUBLE" property="adjustfactor" />
    <result column="isCheck" jdbcType="VARCHAR" property="isCheck" />
  </resultMap>
  <sql id="Base_Column_List">
    RiskCode, CompensationRatio, adjustfactor
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultType="com.sinosoft.eflex.model.FDRiskCompensationRatio">
    select
    RiskCode, CompensationRatio, adjustfactor,if(CompensationRatio IN (SELECT CAST(CompensationRatio AS SIGNED) AS CompensationRatio FROM FcDutGradeCompensationRatio WHERE AmountGrageCode = #{amountGrageCode}),'0','1') as isCheck
    from fdriskcompensationratio
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
    <if test="compensationRatio != null and compensationRatio != ''">
      and CompensationRatio = #{compensationRatio,jdbcType=DOUBLE}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fdriskcompensationratio
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and CompensationRatio = #{compensationRatio,jdbcType=DOUBLE}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDRiskCompensationRatio">
    insert into fdriskcompensationratio (RiskCode, CompensationRatio, adjustfactor
      )
    values (#{riskCode,jdbcType=VARCHAR}, #{compensationRatio,jdbcType=DOUBLE}, #{adjustfactor,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDRiskCompensationRatio">
    insert into fdriskcompensationratio
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="compensationRatio != null">
        CompensationRatio,
      </if>
      <if test="adjustfactor != null">
        adjustfactor,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="compensationRatio != null">
        #{compensationRatio,jdbcType=DOUBLE},
      </if>
      <if test="adjustfactor != null">
        #{adjustfactor,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDRiskCompensationRatio">
    update fdriskcompensationratio
    <set>
      <if test="adjustfactor != null">
        adjustfactor = #{adjustfactor,jdbcType=DOUBLE},
      </if>
    </set>
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and CompensationRatio = #{compensationRatio,jdbcType=DOUBLE}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDRiskCompensationRatio">
    update fdriskcompensationratio
    set adjustfactor = #{adjustfactor,jdbcType=DOUBLE}
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and CompensationRatio = #{compensationRatio,jdbcType=DOUBLE}
  </update>
</mapper>