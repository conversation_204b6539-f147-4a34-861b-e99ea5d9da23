<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FPInsureEflexPlanOptionalMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FPInsureEflexPlanOptional">
    <id column="InsureElfexPlanNo" jdbcType="VARCHAR" property="insureElfexPlanNo" />
    <id column="AmountGrageCode" jdbcType="VARCHAR" property="amountGrageCode" />
    <id column="OptDutyCode" jdbcType="VARCHAR" property="optDutyCode" />
    <result column="InsureState" jdbcType="VARCHAR" property="insureState" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    InsureElfexPlanNo, AmountGrageCode, OptDutyCode, InsureState, Operator, OperatorCom, 
    MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fpinsureeflexplanoptional
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fpinsureeflexplanoptional
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deletefPInsureEflexPlanOptional" parameterType="java.lang.String">
    delete from FPInsureEflexPlanOptional
    where InsureElfexPlanNo in (
    	select InsureElfexPlanNo from fpinsureeflexplan
	    where perNo = #{perNo,jdbcType=VARCHAR}
	    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
	    <if test=" personId != null and  personId != '' ">
          AND personId = #{personId,jdbcType=VARCHAR}
        </if>
    )
  </delete>
  <delete id="deletefPfPEflexCheckRuleEflexPlanOptional" parameterType="java.lang.String">
    delete from fPEflexCheckRuleEflexPlanOptional
    where InsureElfexPlanNo in (
    	select InsureElfexPlanNo from fpinsureeflexplan
	    where perNo = #{perNo,jdbcType=VARCHAR}
	    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
	    <if test=" personId != null and  personId != '' ">
          AND personId = #{personId,jdbcType=VARCHAR}
        </if>
    )
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlanOptional">
    insert into fpinsureeflexplanoptional (InsureElfexPlanNo, AmountGrageCode, 
      OptDutyCode, InsureState, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{insureElfexPlanNo,jdbcType=VARCHAR}, #{amountGrageCode,jdbcType=VARCHAR}, 
      #{optDutyCode,jdbcType=VARCHAR}, #{insureState,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlanOptional">
    insert into fpinsureeflexplanoptional
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="insureElfexPlanNo != null">
        InsureElfexPlanNo,
      </if>
      <if test="amountGrageCode != null">
        AmountGrageCode,
      </if>
      <if test="optDutyCode != null">
        OptDutyCode,
      </if>
      <if test="insureState != null">
        InsureState,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="insureElfexPlanNo != null">
        #{insureElfexPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="amountGrageCode != null">
        #{amountGrageCode,jdbcType=VARCHAR},
      </if>
      <if test="optDutyCode != null">
        #{optDutyCode,jdbcType=VARCHAR},
      </if>
      <if test="insureState != null">
        #{insureState,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlanOptional">
    update fpinsureeflexplanoptional
    <set>
      <if test="insureState != null">
        InsureState = #{insureState,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlanOptional">
    update fpinsureeflexplanoptional
    set InsureState = #{insureState,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where InsureElfexPlanNo = #{insureElfexPlanNo,jdbcType=VARCHAR}
      and AmountGrageCode = #{amountGrageCode,jdbcType=VARCHAR}
      and OptDutyCode = #{optDutyCode,jdbcType=VARCHAR}
  </update>
  <insert id="insertfPInsureEflexPlanOptional" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlanOptional">
   INSERT INTO FPInsureEflexPlanOptional
    (InsureElfexPlanNo,AmountGrageCode,OptDutyCode,InsureState,Prem,Operator,OperatorCom,MakeDate, MakeTime,ModifyDate, ModifyTime)
    <foreach collection="list" item="item" index="index" separator="union all">
      SELECT #{list[${index}].insureElfexPlanNo,jdbcType=VARCHAR},
      #{list[${index}].amountGrageCode,jdbcType=VARCHAR },
      #{list[${index}].optDutyCode,jdbcType=VARCHAR},
      #{list[${index}].insureState,jdbcType=VARCHAR},
      #{list[${index}].prem,jdbcType=DOUBLE},
      #{list[${index}].operator,jdbcType=VARCHAR},
      #{list[${index}].operatorCom,jdbcType=VARCHAR},
      #{list[${index}].makeDate,jdbcType=DATE},
      #{list[${index}].makeTime,jdbcType=VARCHAR},
      #{list[${index}].modifyDate,jdbcType=DATE},
      #{list[${index}].modifyTime,jdbcType=VARCHAR}
      from DUAL
    </foreach>
  </insert>
  <insert id="insertfPfPEflexCheckRuleEflexPlanOptional" parameterType="com.sinosoft.eflex.model.FPInsureEflexPlanOptional">
   INSERT INTO fPEflexCheckRuleEflexPlanOptional
    (InsureElfexPlanNo,AmountGrageCode,OptDutyCode,InsureState,Prem,Operator,OperatorCom,MakeDate, MakeTime,ModifyDate, ModifyTime)
    <foreach collection="list" item="item" index="index" separator="union all">
      SELECT #{list[${index}].insureElfexPlanNo,jdbcType=VARCHAR},
      #{list[${index}].amountGrageCode,jdbcType=VARCHAR },
      #{list[${index}].optDutyCode,jdbcType=VARCHAR},
      #{list[${index}].insureState,jdbcType=VARCHAR},
      #{list[${index}].prem,jdbcType=DOUBLE},
      #{list[${index}].operator,jdbcType=VARCHAR},
      #{list[${index}].operatorCom,jdbcType=VARCHAR},
      #{list[${index}].makeDate,jdbcType=DATE},
      #{list[${index}].makeTime,jdbcType=VARCHAR},
      #{list[${index}].modifyDate,jdbcType=DATE},
      #{list[${index}].modifyTime,jdbcType=VARCHAR}
      from DUAL
    </foreach>
  </insert>
  <update id="updateInsureState" parameterType="java.util.Map">
  	update FPInsureEflexPlanOptional set InsureState=#{insureState,jdbcType=VARCHAR}
  	where InsureElfexPlanNo in (
  		select InsureElfexPlanNo from FPInsureEflexPlan where
  		perNo = #{perNo,jdbcType=VARCHAR}
	    and ensureCode = #{ensureCode,jdbcType=VARCHAR}
	    and personId = #{personId,jdbcType=VARCHAR}
  	)
  </update>
</mapper>