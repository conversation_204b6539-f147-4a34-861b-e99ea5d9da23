<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCHealthDesignDetailRelaMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCHealthDesignDetailRela">
    <id column="DesignNo" jdbcType="VARCHAR" property="designNo" />
    <id column="HealthDesignNo" jdbcType="VARCHAR" property="healthDesignNo" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    DesignNo, HealthDesignNo, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fchealthdesigndetailrela
    where DesignNo = #{designNo,jdbcType=VARCHAR}
      and HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByDesignNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCHealthDesignDetailRela">
    select
    <include refid="Base_Column_List" />
    from fchealthdesigndetailrela
    where DesignNo = #{designNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fchealthdesigndetailrela
    where DesignNo = #{designNo,jdbcType=VARCHAR}
      and HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteAllBydesign">
    delete from fchealthdesigndetailrela
    where DesignNo = #{designNo}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetailRela">
    insert into fchealthdesigndetailrela (DesignNo, HealthDesignNo, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{designNo,jdbcType=VARCHAR}, #{healthDesignNo,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetailRela">
    insert into fchealthdesigndetailrela
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="designNo != null">
        DesignNo,
      </if>
      <if test="healthDesignNo != null">
        HealthDesignNo,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="designNo != null">
        #{designNo,jdbcType=VARCHAR},
      </if>
      <if test="healthDesignNo != null">
        #{healthDesignNo,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetailRela">
    update fchealthdesigndetailrela
    <set>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where DesignNo = #{designNo,jdbcType=VARCHAR}
      and HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCHealthDesignDetailRela">
    update fchealthdesigndetailrela
    set Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where DesignNo = #{designNo,jdbcType=VARCHAR}
      and HealthDesignNo = #{healthDesignNo,jdbcType=VARCHAR}
  </update>
  <insert id="insertList" parameterType="java.util.List">
    insert into fchealthdesigndetailrela
    (DesignNo, HealthDesignNo, Operator, OperatorCom,
    MakeDate, MakeTime, ModifyDate,
    ModifyTime) values
    <foreach collection ="list" item="item" index= "index" separator =",">
      (#{item.designNo,jdbcType=VARCHAR},
      #{item.healthDesignNo,jdbcType=VARCHAR},
      #{item.operator,jdbcType=VARCHAR},
      #{item.operatorCom,jdbcType=VARCHAR},
      #{item.makeDate,jdbcType=DATE},
      #{item.makeTime,jdbcType=VARCHAR},
      #{item.modifyDate,jdbcType=DATE},
      #{item.modifyTime,jdbcType=VARCHAR})
    </foreach >
  </insert>
</mapper>