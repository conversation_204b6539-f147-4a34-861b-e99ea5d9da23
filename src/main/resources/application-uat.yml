#==============数据源配置==============#
spring:
  datasource:
    name: mydb
    type: com.alibaba.druid.pool.DruidDataSource
    url: *********************************************************************************************************************************************************************************************************************
    username: u_uflexdb_user
    password: ZcZYOm1YKVDV
    driverClassName: com.mysql.jdbc.Driver
    minIdle: 10
    maxActive: 2000
    initialSize: 10
    timeBetweenEvictionRunsMillis: 1800000
    minEvictableIdleTimeMillis: 1800000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
  #==============redis配置==============#
  redis:
    host: ***********
    port: 6381
    password: A5uysUldsLcPJct
    timeout: 3000
    database: 0

#==============mybatis配置==============#
mybatis:
  mapperLocations: classpath*:/mappers/**/*.xml
  typeAliasesPackage: com.sinosoft.eflex.model
  configuration:
    call-setters-on-nulls: true

#==============图片展示路径请求==============#
fileDisplay.path: http://eflex.uat.e-hqins.com/api/File/fileDisplay?sftpPath=

#==============log配置==============#
logging:
  config: classpath:log.xml
  path: /home/<USER>/hqeflex/logs
  file: ${server.port}
  level:
    root: info
    com.hqins.ihome: debug
    org.springframework.web: info
    com.alibaba.nacos: warn
    io.grpc.netty.shaded.io.grpc.netty: warn
    org.apache.http: warn

#==============pagehelper分页插件配置==============#
pagehelper:
  helper-dialect: mysql
  supportMethodsArguments: true
  reasonable: true
  params: count=countSql

#==============自定义的属性和值配置==============#
myProps:
  #==============redis有效期配置==============#
  TermValidity:
    #验证码
    codeValidity: 300
    #登录时长
    loginValidity: 1200
  #核心webservice配置信息（老核心）
  serviceInfo:
    'serviceUrl': "http://10.9.10.74:7001/services/BenefitService?wsdl"
    'operation': "submitData"
  #核心日常计划路径配置信息（新核心）
  coreServiceInfo:
    'basicsPreCheckUrl': "http://************:8100/gateway/group-insure-service/basicInsureApply/preUW"
    'basicsSignUrl': "http://************:8100/gateway/group-insure-service/basicInsureApply/dealData"
    'naturalPreCheckUrl': "http://************:8100/gateway/group-insure-service/singleInsureApply/preUnderWriting"
    'naturalCheckUrl': "http://************:8100/gateway/group-insure-service/singleInsureApply/dealUnderWriting"
    'naturalSignUrl': "http://************:8100/gateway/group-insure-service/singleInsureApply/signInsurance"
  #电子投保单生成地址
  GenerateImage:
    policyGenerateImageUrl: http://print.e-hqins.com/DZDYWS/hengqin/generateImage.action
    policyDataSearchUrl: http://print.e-hqins.com/DZDYWS/hengqin/dataSearch.action
  #电子投保单签名地址
  policySign:
    policySignAddress: http://eflex.uat.e-hqins.com/File/dailyFileDisplay?
    policySignBaseAddress: http://eflex.uat.e-hqins.com/File/fileDisplay?
  #电商接口配置信息
  DSserviceInfo:
    'url': "https://gateway-uat.e-hqins.com/"
    'version': "1.0.0"
    'appkey': "27199455-2192-4d14-bbc2-433adfedf139"
    'securityType': "b"
    'publicKey': "d41d8cd98f00b204e9800998ecf8427e"
  #e签宝配置信息（企业ocr）
  Etsign:
    'url': "http://smlrealname.tsign.cn:8080/realname/rest/external/organ/orgAuth"
    'ProjectID': "**********"
    'ProjectSecret': "df0443f7c33e5377716364c28b7449d4"
  #签约接口地址信息
  SignSererUrl:
    SignApplySererUrl: http://************/paymentCardPayService/signApply
    SignConfirmSererUrl: http://************/paymentCardPayService/signConfirm
    Sourse: H06
  #签约接口中台地址信息
  BankSign.MiddlegroundUrl:
    SignApplyUrl: https://t-u.e-hqins.com/service/api-bankSignApply
    SignConfirmUrl: https://t-u.e-hqins.com/service/api-bankSignSure
    appId: yf-app
    appSecret: 1FD3bAadF1933B61
  #中台支付中心接口地址信息
  PayCenter:
    transSource: YF
    getPayUrltransCode: ISHARE-PAY-API-GETPAYURL
    payKind: H5
    channelCode: 11
    pageBackUrl: http://eflex-uat.uat.e-hqins.com/phone/#/InsurancePay
    dataBackUrl: https://uat-uflexbk.e-hqins.com/hqeflex/services/payStatusNotice
    payStatusQuery: ISHARE-PAY-API-QUERYPAYSTATUS
    payUrl: http://172.16.15.103:9527/service/api-getPayUrl
    app_id: yf-pay
    app_secret: ibsnz5rytovfv18ohSb2
    payQueryUrl: http://172.16.15.103:9527/service/api-queryPayStatus
  #==============中台人脸识别接口地址信息==============#
  faceDiscern:
    face_appId: yf-app
    face_appSecret: 1FD3bAadF1933B61
    face_Url: https://ishare-gw-uat.e-hqins.com/intelligence/verify/faceid/wxh5/geturl
    face_QueryUrl: https://ishare-gw-uat.e-hqins.com/intelligence/verify/faceid/wxh5/getdata
    face_transCode: IS_FACEID_001
    face_channelSource: 4
    face_actionType: H5
    face_ruleId: 0
    face_redirectUrl: http://eflex-uat.uat.e-hqins.com/phone/#/InsuranceSignature
    face_redirectUrlShare: http://eflex-uat.uat.e-hqins.com/phone/
  #==============中台人脸识别接口地址信息==============#
  PrintPlatform:
    guaranteeUrl: http://print.e-hqins.com/DZDYWS/hengqin/dataCancel.action
  #==============个人ocr配置==============#
  ocrMsg:
    ocrUrl: https://ishare-gw-uat.e-hqins.com/intelligence/ocr/all
    ocrTransCode: IS_OCR_001
    ocrChannelSource: 7
    ocrActionType: API
    ocrAction: IDCard
    ocrApp_id: yf-ocr
    ocrApp_secret: 77465098786c4cbe8584
  #============微信分享配置 公众号appid、公众号开发者秘钥==============#
  WeChatShareConfig:
    Share_AppId: wx6a9f08a1bc1fbfd7
    Share_AppSecret: 0eaa38c790077800512d7deed33bb37c
    GetAccessToken_URL: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    GetTicket_URL: https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi
  #==============保单打印系统配置--测试==============#
  CASystem:
    #下行--信息发送网关
    caHost: **********
    caPort: 6666
  #==============配置静态资源==============#
  spring:
    mvc:
      static-path-pattern: /images/**
    resources:
      static-locations: file:/home/<USER>/hqeflex/uploadfile/
  #==============满天星短信发送平台==============#
  sendMessage:
    url: http://***********:8080/sendMessage
    product: hengqinlife_staff_benefits
    application: hengqinlife_staff_benefits
    appKey: 39a56809459311eb8d2d70106faecec4
