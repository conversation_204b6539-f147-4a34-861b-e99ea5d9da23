package com.sinosoft.eflex.generator;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.FileType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.io.File;
import java.util.*;

/**
 * 增强版代码生成器
 * 功能：
 * 1. 生成完整的CRUD代码
 * 2. 自动识别逻辑删除字段
 * 3. 自动填充创建/更新时间
 * 4. 生成Swagger注解
 * 5. 生成自定义Service方法
 */
public class CodeGenerator {

    // ========== 配置区 ==========
    private static final String DB_URL = "*********************************************************************************************************************************************************************************************************************";
    private static final String DB_DRIVER = "com.mysql.jdbc.Driver";
    private static final String DB_USERNAME = "u_uflexdb_user";
    private static final String DB_PASSWORD = "Uflex_12345";

    private static final String AUTHOR = "你猜";
    private static final String BASE_PACKAGE = "com.sinosoft.eflex";
    private static final boolean ENABLE_SWAGGER = true;

    // 自动填充字段配置（按需修改）
    private static final String CREATE_TIME_FIELD = "create_time";
    private static final String UPDATE_TIME_FIELD = "update_time";
    private static final String LOGIC_DELETE_FIELD = "is_deleted";
    private static final String[] TABLE_NAMES = new String[]{
            "insured_reading_log"
    };
    // ========== 生成逻辑 ==========
    public static void main(String[] args) {
        System.out.println("========== 开始生成表 ==========");
        AutoGenerator generator = new AutoGenerator();

        // 全局配置（关闭文件覆盖）
        GlobalConfig globalConfig = new GlobalConfig()
                .setOutputDir(System.getProperty("user.dir") + "/src/main/java")
                .setAuthor(AUTHOR)
                .setOpen(false)
                .setFileOverride(false) // 关键修改点！！！
                .setDateType(DateType.TIME_PACK)
                .setSwagger2(ENABLE_SWAGGER)
                .setControllerName("%sController")
                .setServiceName("%sService")
                .setServiceImplName("%sServiceImpl")
                .setMapperName("%sMapper")
                .setXmlName("%sMapper")
                .setBaseResultMap(true)
                .setBaseColumnList(true);
        // 自定义XML输出路径
        String projectPath = System.getProperty("user.dir");

        // 数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig()
                .setUrl(DB_URL)
                .setDriverName(DB_DRIVER)
                .setUsername(DB_USERNAME)
                .setPassword(DB_PASSWORD)
                .setDbType(DbType.MYSQL);

        // 包配置
        PackageConfig packageConfig = new PackageConfig()
                .setParent(BASE_PACKAGE)
                .setEntity("model.entity")
                .setMapper("dao")
                .setService("service")
                .setServiceImpl("service.impl")
                .setController("ctrl");

        // 策略配置
        StrategyConfig strategy = new StrategyConfig()
                .setNaming(NamingStrategy.underline_to_camel)
                .setColumnNaming(NamingStrategy.underline_to_camel)
                .setEntityLombokModel(true)
                .setRestControllerStyle(true)
                .setControllerMappingHyphenStyle(true)
                .setTablePrefix("t_", "tb_")
                .setInclude(TABLE_NAMES)
                .setLogicDeleteFieldName(LOGIC_DELETE_FIELD)
                .setVersionFieldName("version")
                .setEntityTableFieldAnnotationEnable(true)
                .setTableFillList(getTableFills());

        // 模板配置
        TemplateConfig templateConfig = new TemplateConfig()
                .setController("/templates/controller.java")
                .setService("/templates/service.java")
                .setServiceImpl("/templates/serviceImpl.java")
                .setMapper("/templates/mapper.java")
                .setXml(null)
                .setEntity("/templates/entity.java");

        // 自定义文件输出配置（关键修改部分）
        InjectionConfig injectionConfig = new InjectionConfig() {
            @Override
            public void initMap() {
                Map<String, Object> map = new HashMap<>();
                map.put("date", new Date());
                this.setMap(map);
            }
        }.setFileCreate(new IFileCreate() {
            @Override
            public boolean isCreate(ConfigBuilder configBuilder, FileType fileType, String filePath) {
                if (fileType == FileType.ENTITY) {
                    return true; // 总是覆盖实体类
                }
                // 检查文件是否存在，存在则不生成
                File file = new File(filePath);
                boolean exists = file.exists();
                if (exists) {
                    System.out.println("[跳过] 文件已存在: " + filePath);
                    return false;
                }
                // 允许生成文件
                return true;
            }
        });
        List<FileOutConfig> focList = new ArrayList<>();
        focList.add(new FileOutConfig("/templates/mapper.xml.ftl") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/src/main/resources/mappers/" + tableInfo.getMapperName() + ".xml";
            }
        });
        injectionConfig.setFileOutConfigList(focList);

        generator.setGlobalConfig(globalConfig)
                .setDataSource(dataSourceConfig)
                .setPackageInfo(packageConfig)
                .setStrategy(strategy)
                .setTemplate(templateConfig)
                .setCfg(injectionConfig)
                .setTemplateEngine(new FreemarkerTemplateEngine());

        generator.execute();
        System.out.println("========== 生成表完成 ==========\n");
    }

    // 检查文件是否存在
    private static boolean checkFileExists(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            System.out.println("[跳过] 文件已存在: " + filePath);
            return true;
        }
        return false;
    }



    // 获取自动填充字段配置
    private static List<TableFill> getTableFills() {
        List<TableFill> tableFills = new ArrayList<>();
        tableFills.add(new TableFill(CREATE_TIME_FIELD, FieldFill.INSERT));
        tableFills.add(new TableFill(UPDATE_TIME_FIELD, FieldFill.INSERT_UPDATE));
        return tableFills;
    }
}