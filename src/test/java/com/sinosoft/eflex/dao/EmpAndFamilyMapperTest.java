package com.sinosoft.eflex.dao;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.insurePlanPage.PersonInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2021/6/3 10:23
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@Slf4j
@ActiveProfiles("uat")
public class EmpAndFamilyMapperTest {

    @Autowired
    private EmpAndFamilyMapper empAndFamilyMapper;

    @Test
    public void selectPersonInfo() {
        String personId = "00000000000000002115";
        PersonInfo personInfo = empAndFamilyMapper.selectPersonInfo(personId);
        System.out.println(JSONObject.toJSONString(personInfo));

    }

        @Test
        public void logTest(){
            int x = 1;
            int y = 2;
            System.out.println("x = " + x + ", y = " + y + ", x + y = " + (x + y)); // 传统做法
            log.trace("111x = {}, y = {}, x + y = {}", x , y , x + y); // 使用日志输出变量的做法
            log.debug("222x = {}, y = {}, x + y = {}", x , y , x + y); // 使用日志输出变量的做法
            log.info("333x = {}, y = {}, x + y = {}", x , y , x + y); // 使用日志输出变量的做法
            log.warn("444x = {}, y = {}, x + y = {}", x , y , x + y); // 使用日志输出变量的做法
            log.error("555x = {}, y = {}, x + y = {}", x , y , x + y); // 使用日志输出变量的做法
        }
}