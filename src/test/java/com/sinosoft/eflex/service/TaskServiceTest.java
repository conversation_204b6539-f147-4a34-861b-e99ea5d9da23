package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.AddressEntity.CoreRequestBody;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.face.FaceStatusQuery;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.HttpUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.sinosoft.eflex.dao.FcPerImpartResultMapper;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @date 2020/12/30 16:17
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("uat")
public class TaskServiceTest {

    @Autowired
    private TaskService taskService;
    @Autowired
    private FcPerImpartResultMapper fcPerImpartResultMapper;

    @Test
    public void fixeMakeDefaultPlan() {
//        "FL000000000001001897"
        taskService.fixeMakeDefaultPlan(new FCEnsure());

    }

    @Test
    public void insureOnePolicy() {
        // ContInfo contInfo = new ContInfo();
        //
        // List<InsuredInfo> insuredInfoList = new ArrayList<>();
        //
        // InsuredInfo insuredInfo = new InsuredInfo();
        // List<InsuredImpart> insuredImpartList =
        // fcPerImpartResultMapper.selectByOrderItemNo("00000000000000015476");
        // for (InsuredImpart insuredImpart : insuredImpartList) {
        // System.out.println(insuredImpart.getImpartContent());
        // }
        //
        // insuredInfo.setInsuredImpartList(insuredImpartList);
        // insuredInfoList.add(insuredInfo);
        // contInfo.setInsuredInfoList(insuredInfoList);
        // XStream xStream = new XStream(new DomDriver("UTF-8", new
        // XmlFriendlyNameCoder("-_", "_")));
        // xStream.alias("InsuredImpart", InsuredImpart.class);
        // xStream.alias("Page", Page.class);
        // boolean flag = XmlUtil.createXml(xStream, contInfo,
        // "C:/Users/<USER>/Desktop/" + DateTimeUtil.getCurrentDate() + ".xml");

        // 转义前 <html><body>123'123'</body></html>
        // String escape =
        // "转义后的一句话：&lt;html&gt;&lt;body&gt;123&#039;123&#039;&lt;/body&gt;&lt;/html&gt;";
        // 转义方法一（需要先引入依赖）
        // <dependency>
        // <groupId>cn.hutool</groupId>
        // <artifactId>hutool-all</artifactId>
        // <version>5.7.4</version>
        // </dependency>
        // String unescapeXml1 = HtmlUtil.unescape(escape);
        // System.out.println(unescapeXml1);
        // 转义方法二
        // String unescapeXml2 = StringEscapeUtils.unescapeXml(escape);
        // System.out.println(unescapeXml2);

    }

    public static void main(String[] args) throws UnsupportedEncodingException {

        CoreRequestBody requestBody = CoreRequestBody.builder()
                .transCode("POLICY-MANAGE-SERVICE-GRP-ENDORSE-SEND")
                .transNo(String.valueOf(System.currentTimeMillis()))
                .transSN(String.valueOf(System.currentTimeMillis()))
                .transSource("YF")
                .transTime(DateTimeUtil.getCurrentDateTime()).build();
        String url = "https://ishare-gw-uat.e-hqins.com/service/grp-endorse-send?grpContNo="+"GP4423000073";
        String s = HttpUtil.postHttpRequestJson(url, JSONObject.toJSONString(requestBody), "yf-app", "1FD3bAadF1933B61");
        System.out.println("s = " + s);
    }
    public static void main1(String[] args) {
//		FaceUrl faceUrl = new FaceUrl();
//		faceUrl.setTransCode("IS_FACEID_001");
//		faceUrl.setRuleId("0");
//		faceUrl.setRedirectUrl("https://www.baidu.com");
//		faceUrl.setChannelSource("4");
//		faceUrl.setActionType("H5");
//		faceUrl.setIdCard("130925199210185119");
//		faceUrl.setName("韩乐");
//		String jsonString = JSONObject.toJSONString(faceUrl);
//		System.out.println(jsonString);
//		String postHttpRequestJson = HttpUtil.postHttpRequestJson("https://ishare-gw-uat.e-hqins.com/intelligence/verify/faceid/wxh5/geturl", jsonString, "yf-app", "1FD3bAadF1933B61");
//		System.out.println(postHttpRequestJson);

        FaceStatusQuery faceStatusQuery = new FaceStatusQuery();
        faceStatusQuery.setTransCode("IS_FACEID_001");
        faceStatusQuery.setInfoType("13");
        faceStatusQuery.setChannelSource("4");
        faceStatusQuery.setBizToken("357FB7A7-13E3-4C46-8EFC-E29B038C02C2");
        faceStatusQuery.setActionType("H5");
        String postHttpRequestJson2 = HttpUtil.postHttpRequestJson("https://ishare-gw-uat.e-hqins.com/intelligence/verify/faceid/wxh5/getdata", JSONObject.toJSONString(faceStatusQuery), "yf-app", "1FD3bAadF1933B61");
    }
}