package com.sinosoft.eflex.service.edor;

import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.edor.EdoraTrialInfo;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.dailyinsure.DailyPlanPhoneService;
import com.sinosoft.eflex.enums.EdorTypeEnum;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2020/11/19 21:12
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("dev")
public class EdorServiceTest {

    private static final Logger log = LoggerFactory.getLogger(EdorServiceTest.class);


    @Autowired
    private MyProps myProps;

    @Autowired
    private DailyPlanPhoneService dailyPlanPhoneService;
    @Autowired
    private EdorService edorService;
    @Autowired
    private MaxNoService maxNoService;

    @Test
    public void uploadZipOrRARFile() {
        System.out.println(myProps.getFileDisplayPath());

        String prefixSingleFileName = "121313dsdsdsdsdsdsdsds大苏打实打实-_";
        if (!prefixSingleFileName.matches("^[-\\u2E80-\\uFE4F_a-zA-Z0-9]+$")) {
            System.out.println("保全内部文件名称不正确，不能含有特殊字符!");
        }
    }

    public static void main(String[] args) {
        String prefixSingleFileName = "=";
        if (!prefixSingleFileName.matches("^[-\\u2E80-\\uFE4F_a-zA-Z0-9]+$")) {
            System.out.println("保全内部文件名称不正确，不能含有特殊字符!");
        }
    }

    @Test
    public void test1(){
        String token = "687895f765be4ee1ad2187eebe6b3c0c";

        String json = "{\"decBatch\":\"00000000000000000406\",\"addBatch\":\"00000000000000000301\",\"grpContNo\":\"GP4421000164\",\"policyHolderChgTrialIO\":{\"policyHolderInfo\":{\"nzProportion\":\"\",\"corporation\":\"\",\"email\":\"\",\"grpAddress\":\"\",\"grpName\":\"\",\"grpZipCode\":\"\",\"linkMan1\":\"\",\"mobilePhone1\":\"\",\"getFlag\":\"\",\"bankCode\":\"\",\"bankAccNo\":\"\",\"oldcorporation\":\"\",\"oldemail\":\"\",\"oldgrpAddress\":\"\",\"oldgrpName\":\"\",\"oldgrpZipCode\":\"\",\"oldlinkMan1\":\"\",\"oldmobilePhone1\":\"\",\"oldateFlag\":\"\",\"oldbankCode\":\"\",\"oldbankAccNo\":\"\"},\"edorAppNo\":\"\",\"edorFlag\":false},\"insuredChgTrialIO\":{\"insurersList\":[{\"title\":\"资料变更\",\"falsh\":true,\"showa\":false,\"birthday\":\"\",\"email\":\"\",\"newIdType\":\"\",\"idExpDate\":\"\",\"newIdNo\":\"\",\"medicareStatus\":\"\",\"mobile\":\"\",\"newname\":\"\",\"nationality\":\"\",\"occupationCode\":\"\",\"occupationType\":\"\",\"sex\":\"\",\"iDType\":\"\",\"idNo\":\"\",\"oldidExpDate\":\"\",\"oldbirthday\":\"\",\"oldemail\":\"\",\"oldmedicareStatus\":\"\",\"oldmobile\":\"\",\"name\":\"\",\"oldnationality\":\"\",\"oldoccupationCode\":\"\",\"oldoccupationType\":\"\",\"oldsex\":\"\",\"oldiDType\":\"\",\"oldidNo\":\"\",\"checked\":false,\"checkeds\":false,\"checked1s\":false,\"occupationCode01\":\"\",\"occupationCode02\":\"\",\"occupationCode03\":\"\",\"occupationCode04\":\"\"}],\"edorAppNo\":\"\",\"edorFlag\":false},\"bnfChgTrialIO\":{\"insuredList\":[{\"bnfList\":[{\"bnfGrade\":\"\",\"bnfIdNo\":\"\",\"bnfIdType\":\"\",\"bnfLot\":\"\",\"bnfName\":\"\",\"bnfSex\":\"\",\"birthDay\":\"\",\"IdTimeEnd\":\"\",\"relationToInsured\":\"\"}],\"oldbnfList\":[{\"oldbnfGrade\":\"\",\"oldbnfIdNo\":\"\",\"oldbnfIdType\":\"\",\"oldbnfLot\":\"\",\"oldbnfName\":\"\",\"oldbnfSex\":\"\",\"oldidTimeEnd\":\"\",\"oldbirthDay\":\"\",\"oldrelationToInsured\":\"\"}],\"title\":\"资料变更\",\"falsh\":true,\"iDType\":\"\",\"idNo\":\"\",\"name\":\"\",\"oldiDType\":\"\",\"oldidNo\":\"\",\"oldname\":\"\",\"show\":false}],\"edorAppNoBC\":\"\",\"edorFlag\":false},\"NIFlag\":true,\"ZTFlag\":false,\"HrarBatch\":\"00000000000000000362\",\"HNIFlag\":false}";
        EdoraTrialInfo edoraTrialInfo = JSONObject.parseObject(json, EdoraTrialInfo.class);

        edorService.trialIO(edoraTrialInfo, token);
    }

    @Test
    public void test2() {
        String edorType = "";

        switch (edorType) {
        case "NI":
            System.out.println("NI");
            break;
        case "ZT":
            System.out.println("ZT");
            break;
        default:
            System.out.println("default");
            break;
        }
        switch (EdorTypeEnum.getTypeByCode(edorType)) {
        case EDORNI:
            System.out.println("NI");
            break;
        case EDORZT:
            System.out.println("ZT");
            break;
        default:
            System.out.println("default");
            break;
        }
    }

    @Test
    public void test3() {
        /**
         * 保单挂起的校验
         */
        RemoteDelegate rd = RemoteDelegate.getInstance();
        String requestXml = "<?xml version=\"1.0\"?>\n"
                + "<RequestInfo>\n"
                + "\t<HEAD>\n"
                + "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n"
                + "\t\t<TransType>YUG013</TransType>\n"
                + "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n"
                + "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n"
                + "\t</HEAD>\n"
                + "\t<BODY>\n"
                + "\t\t\t<GrpContNo>" + "GP4421000184" + "</GrpContNo>\n"
                + "\t</BODY>\n"
                + "</RequestInfo>";
        log.info("调用核心保全挂起查询接口请求报文：" + requestXml);
        long startStamp = System.currentTimeMillis();
        boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"),
                myProps.getServiceInfo().get("operation"), "BenefitService", requestXml);
        long endStamp = System.currentTimeMillis();
        log.info("调用核心保全挂起查询接口用时：" +  (endStamp - startStamp));
    }


}