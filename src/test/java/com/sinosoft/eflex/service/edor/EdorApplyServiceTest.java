package com.sinosoft.eflex.service.edor;

import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.GrpContInfo;
import com.sinosoft.eflex.model.edor.GrpContQuery;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import com.sinosoft.eflex.util.rest.exception.BusinessException;
import com.thoughtworks.xstream.XStream;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/9/14 10:35
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("uat")
public class EdorApplyServiceTest {

    @Autowired
    private EdorApplyServiceContext edorApplyServiceContext;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private MyProps myProps;


    Logger log = LoggerFactory.getLogger(EdorApplyServiceTest.class);


    @Test
    public void dealEdorApply() {

        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectGrpInfo1("00000000000000000242");
        // 构建一个文本消息
        GrpContQuery grpContQuery = new GrpContQuery();
        grpContQuery.setGrpIdType(fcGrpInfo.getGrpIdType());
        grpContQuery.setGrpIdTypeName(fcGrpInfo.getGrpIdTypeName());
        grpContQuery.setGrpIdNo(fcGrpInfo.getGrpIdNo());
        grpContQuery.setGrpContNo("GP4422000004");
        grpContQuery.setPageNum("1");
        grpContQuery.setPageSize("10");
        XStream xStream = new XStream();
        xStream.alias("GrpContQuery", GrpContQuery.class);
        String reqXmlBody = xStream.toXML(grpContQuery);
        String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" + "<RequestInfo>\n" + "\t<HEAD>\n" +
                /* 交易流水号 */
                "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                /* 接口交易类型 */
                "\t\t<TransType>BF0004</TransType>\n" +
                /* 交易日期 */
                "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                /* 交易时间 */
                "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" + "\t</HEAD>\n" + "\t<BODY>\n" + reqXmlBody + "\t</BODY>\n" + "</RequestInfo>";
        log.info("企业保单查询接口请求报文: {}", reqXml);
        long startStamp = System.currentTimeMillis();
        //将请求报文发送核心
        RemoteDelegate rd = RemoteDelegate.getInstance();
        Map<String, String> myPrpsMap = myProps.getServiceInfo();
        boolean success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml); //BenefitService
        long endStamp = System.currentTimeMillis();
        log.info("调用核心接口用时：" + (endStamp - startStamp));
        if (success) {
            //接收返回结果
            Map<String, Object> responseXml = rd.getResult();
            List<GrpContInfo> list = (List<GrpContInfo>) responseXml.get("GrpContList");
            if (list.size() != 1) {
                throw new BusinessException("核心保单查询接口有误！");
            } else {
                System.out.println("核心保单查询接口成功！");
            }
        } else {
            throw new BusinessException("核心保单查询接口调用失败！");
        }

    }
}