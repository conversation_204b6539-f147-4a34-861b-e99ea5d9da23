package com.sinosoft.eflex.service.dailyinsure;

import com.sinosoft.eflex.model.User;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/11/30
 * @desc
 */
@Data
public class test {

    private final User user;

    private final String ss;

    public test(){
        user = new User();
        ss = "444";
        System.out.println(user);
        System.out.println(ss);
    }

    public static void main(String[] args) {
        new test();
    }

}
