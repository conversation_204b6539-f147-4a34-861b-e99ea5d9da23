//package com.sinosoft.eflex.service.dailyinsure;
//
//import com.sinosoft.eflex.EflexApplication;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.SpringApplicationConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
///**
// * <AUTHOR>
// * @date 2020/6/15 15:29
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@WebAppConfiguration
//@SpringApplicationConfiguration(EflexApplication.class)
////@ActiveProfiles("dev")
//public class DailyMakeProposalFormServiceTest {
//
//    @Autowired
//    private DailyMakeProposalFormService dailyMakeProposalFormService;
//
//    @Test
//    public void makeTest(){
//        dailyMakeProposalFormService.MakeProposalForm("FL000000000001001604","00000000000001008245","00000000000001004087");
//    }
//
//    @Test
//    public void makeProposalForm() {
//        System.out.println(String.valueOf(null));
//    }
//
//    public static void main(String[] args) {
//        System.out.println((String)null);
//    }
//
////    @Test
////    public void MakeProposalForm(String token, String ensureCode) {
////
////        /** 请求报文拼接 */
////        XStream xStream = new XStream(new DomDriver());
////        DATASETS datasets = new DATASETS();
////
////        datasets.setControl("01");
////
////        ImageTypes imageTypes = new ImageTypes();
////        imageTypes.setImageType("411011");
////        datasets.setImageTypes(imageTypes);
////
////        DATASET dataset = new DATASET();
////        dataset.setPrtNo("A41101199016420");
////        dataset.setOrderNo("25355223525");
////        dataset.setManageComCode("8644040506");
////        dataset.setManageComName("多元营销部");
////        dataset.setAgentCode("11303A002285");
////        dataset.setAgentName("呵呵呵");
////        dataset.setAgentPhone("15735789099");
////        dataset.setCurrencyType("");
////        dataset.setPayMoneySumWrods("叁仟柒佰柒拾捌元壹角叁分");
////        dataset.setPayMoneySumFigures("3778.13");
////        dataset.setPayMode("银行转账");
////        dataset.setNewPayMode("银行转账");
////        dataset.setPayIntv("年交");
////        dataset.setRNewFlag("是");
////        dataset.setSocialInsuFlag("是");
////        dataset.setBonusGetMode("年金领取");
////        dataset.setAnnuityGetMode("年金领取");
////        dataset.setAnnuityGetIntv("月");
////        dataset.setBankName("中国工商银行");
////        dataset.setBankAccNo("6222 0210 0111 6245 702");
////        dataset.setBankAccName("多一的");
////        dataset.setBankAccType("01");
////        dataset.setSignDate("2020-01-08");
////        dataset.setAgentSignDate("2020-01-08");
////        dataset.setLoanType("");
////        dataset.setLoanOrg("");
////        dataset.setLoanContNo("");
////        dataset.setLoanMoney("");
////        dataset.setAppntName("多一的");
////        dataset.setAppntBirthday("1983-01-14");
////        dataset.setAppntSex("男");
////        dataset.setAppntNationality("中国香港");
////        dataset.setAppntIdTypeName("港澳居民来往大陆通行证");
////        dataset.setAppntIdNo("H75625396");
////        dataset.setAppntIdExpDate("2022-01-14");
////        dataset.setAppntOccupationName("企业经理");
////        dataset.setAppntOccupationCode("1050102");
////        dataset.setAppntWorkPlace("啦啦啦啦咯哦33");
////        dataset.setAppntAnnualIncome("20");
////        dataset.setAppntAddress("哭哭哭哭哭哭33");
////        dataset.setAppntZipCode("");
////        dataset.setAppntPhone("13886562565");
////        dataset.setAppntHomePhone("6666666");
////        dataset.setAppntEmail("<EMAIL>");
////        //Insured第一层
////        InsuredsParent insuredsParent = new InsuredsParent();
////        //Insured第二层
////        List<InsuredsItem> Insured = new ArrayList<>();
////        //Insured第三层
////        InsuredsItem insuredsItem = new InsuredsItem();
////        insuredsItem.setInsuredType("第一被保险人");
////        insuredsItem.setInsuredName("呵呵");
////        insuredsItem.setInsuredBirthday("1989-01-14");
////        insuredsItem.setInsuredSex("女");
////        insuredsItem.setInsuredNationality("中国香港");
////        insuredsItem.setInsuredIdTypeName("港澳居民来往大陆通行证");
////        insuredsItem.setInsuredIdNo("H52456664");
////        insuredsItem.setInsuredIdExpDate("2022-01-14");
////        insuredsItem.setInsuredOccupationName("企业经理");
////        insuredsItem.setInsuredOccupationCode("1050102");
////        insuredsItem.setInsuredWorkPlace("啦啊咯咯的看看");
////        insuredsItem.setInsuredAnnualIncome("20");
////        insuredsItem.setInsuredAddress("他今天啦啦啦333");
////        insuredsItem.setInsuredZipCode("");
////        insuredsItem.setInsuredPhone("13586282565");
////        insuredsItem.setInsuredHomePhone("5555555");
////        insuredsItem.setInsuredEmail("<EMAIL>");
////        insuredsItem.setNeedConfirmationFlag("是");
////        insuredsItem.setAppntRelationToInsured("02");
////        //Insured->BnfInfo第一层
////        BnfInfos bnfInfos = new BnfInfos();
////        //Insured->BnfInfo第二层
////        BnfInfosItem bnfInfoItem = new BnfInfosItem();
////        insuredsItem.setBnfInfos(bnfInfos);
////
////        insuredsParent.setInsured(Insured);
////        dataset.setInsureds(insuredsParent);
////
////
////    }
//}