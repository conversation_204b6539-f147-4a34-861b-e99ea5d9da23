package com.sinosoft.eflex.service.dailyinsure;

import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FCPlanRiskDutyMapper;
import com.sinosoft.eflex.dao.FDCodeMapper;
import com.sinosoft.eflex.model.FDCode;
import com.sinosoft.eflex.service.PlanMakeService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/28 18:15
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("uat")
public class DailyPlanServiceTest {

    @Autowired
    private DailyPlanService dailyPlanService;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private PlanMakeService planMakeService;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;

    @Test
    public void saveissuredInfo() {
        // String authorization = "27eb573696844a8a879b207abb31f758";
        //
        // String requestJson = "{\n" + " 'ZipCode':'100000',\n" + "
        // 'regAddress':'海南大道滨河大街联防路',\n" + " 'ensureCode':'FL000000000000001073',\n" +
        // " 'num':'1',\n" + " 'email1':'<EMAIL>',\n" + "
        // 'dailyPrem':'179130.00',\n" + " 'insuredAmount':'50',\n" + "
        // 'insurePeriod':'02',\n" + " 'payFrequency':'1',\n" + " 'payPeriod':'01',\n" +
        // " 'name':'铁锤一',\n" + " 'department':'金融营销解决方案事业部',\n" + " 'country':'CHN',\n"
        // + " 'sex':'0',\n" + " 'birthDay':'1984-12-12',\n" + " 'openBank':'',\n" + "
        // 'mobilePhone':'***********',\n" + " 'productCode':'',\n" + "
        // 'dutyCode':'ID6383',\n" + " 'iDType':'0',\n" + " 'codeType':'',\n" + "
        // 'oldiDNo':'',\n" + " 'iDNo':'370321198412120710',\n" + "
        // 'idTypeEndDay':'2026-11-16',\n" + " 'occupationType':'',\n" + "
        // 'occupationCode':'2022111',\n" + " 'joinMedProtect':'',\n" + "
        // 'openAccount':'',\n" + " 'perNo':'',\n" + " 'perTempNo':'',\n" + "
        // 'staffGrp':'',\n" + " 'familyGrp':'',\n" + " 'occupationCode01':'',\n" + "
        // 'occupationCode02':'',\n" + " 'occupationCode03':'',\n" + "
        // 'occupationCode04':'',\n" + " 'idTypeEndDate':'',\n" + "
        // 'grpNo':'00000000000000000230',\n" + " 'province':'460000',\n" + "
        // 'city':'460100',\n" + " 'area':'460106',\n" + "
        // 'GrpOrderNo':'00000000000000000614'\n" + "}";
        //
        // Map<String, String> params = new HashMap<>();
        //
        // Map parseObject = JSONObject.parseObject(requestJson, params.getClass());
        //
        // System.out.println(parseObject);

        //请求保存人员信息
//        dailyPlanService.saveissuredInfo(authorization,parseObject);

        // ResponseResult responseResult = new ResponseResult();
        // responseResult.setSuccess(false);
        // if (responseResult.getSuccess().equals(Boolean.FALSE)) {
        // System.out.println("aaaaa");
        // }
        List<String> list1 = Arrays.asList("GP4421000136", "GP4421000137", "GP4421000138", "GP4421000140",
                "GP4421000139", "GP4421000145", "GP4421000144", "GP4421000146", "GP4421000141", "GP4421000142",
                "GP4421000149", "GP4421000155", "GP4421000152", "GP4421000156", "GP4421000151", "GP4421000153",
                "GP4421000154", "GP4421000150", "GP4421000148", "GP4421000158", "GP4421000159", "GP4421000160",
                "GP4421000161", "GP4421000162", "GP4421000163", "GP4421000164", "GP4421000166", "GP4421000167",
                "GP4421000165", "GP4421000174", "GP4421000170", "GP4421000172", "GP4421000173", "GP4421000175",
                "GP4421000171", "GP4421000188", "GP4421000191", "GP4421000187", "GP4421000184", "GP4421000181",
                "GP4421000185", "GP4421000177", "GP4421000178", "GP4421000183", "GP4421000189", "GP4421000190",
                "GP4421000179", "GP4421000180", "GP4421000186", "GP4421000176", "GP4421000203", "GP4421000209",
                "GP4421000204", "GP4421000205", "GP4421000207", "GP4421000206", "GP4421000194", "GP4421000195",
                "GP4421000208", "GP4421000193", "GP4421000192", "GP4421000199", "GP4421000198", "GP4421000196",
                "GP4421000200", "GP4421000201", "GP4421000202", "GP4421000197", "GP4421000211", "GP4421000215",
                "GP4421000210", "GP4421000217", "GP4421000212", "GP4421000213", "GP4421000216", "GP4421000214",
                "GP4421000223", "GP4421000221", "GP4421000222", "GP4421000224", "GP4421000220", "GP4421000219",
                "GP4421000230", "GP4421000234", "GP4421000235", "GP4421000238", "GP4421000241", "GP4421000240",
                "GP4421000239", "GP4421000236", "GP4421000263", "GP4421000260", "GP4421000264", "GP4421000262",
                "GP4421000261", "GP4421000265", "GP4421000259", "GP4421000284", "GP4421000271", "GP4421000268",
                "GP4421000278", "GP4421000272", "GP4421000281", "GP4421000267", "GP4421000277", "GP4421000275",
                "GP4421000269", "GP4421000270", "GP4421000276", "GP4421000296", "GP4421000294", "GP4421000297",
                "GP4421000295", "GP4421000300", "GP4421000299", "GP4421000298", "GP4421000301", "GP4421000288",
                "GP4421000289", "GP4421000287", "GP4421000290", "GP4421000302", "GP4421000310", "GP4421000305",
                "GP4421000308", "GP4421000307", "GP4421000309", "GP4421000304", "GP4421000303", "GP4421000306",
                "GP4421000312", "GP4421000327", "GP4421000320", "GP4421000317", "GP4421000318", "GP4421000326",
                "GP4421000324", "GP4421000323", "GP4421000316", "GP4421000330", "GP4421000331", "GP4421000332",
                "GP4421000333", "GP4421000335", "GP4421000338", "GP4421000340", "GP4421000342", "GP4421000349",
                "GP4421000352");

        // List<FCGrpInfo> fcGrpInfo =
        // fcGrpInfoMapper.getAllGrpInfo("00000000000001000233");
        // System.out.println(fcGrpInfo);


        List<String> list2 = Arrays.asList("GP4421000137", "GP4421000138", "GP4421000139", "GP4421000140",
                "GP4421000141", "GP4421000144", "GP4421000145", "GP4421000149", "GP4421000150", "GP4421000151",
                "GP4421000153", "GP4421000154", "GP4421000155", "GP4421000156", "GP4421000158", "GP4421000159",
                "GP4421000160", "GP4421000163", "GP4421000165", "GP4421000166", "GP4421000167", "GP4421000170",
                "GP4421000171", "GP4421000172", "GP4421000173", "GP4421000174", "GP4421000175", "GP4421000176",
                "GP4421000177", "GP4421000178", "GP4421000179", "GP4421000180", "GP4421000181", "GP4421000183",
                "GP4421000184", "GP4421000185", "GP4421000186", "GP4421000187", "GP4421000188", "GP4421000189",
                "GP4421000190", "GP4421000191", "GP4421000192", "GP4421000193", "GP4421000194", "GP4421000195",
                "GP4421000196", "GP4421000197", "GP4421000198", "GP4421000199", "GP4421000200", "GP4421000201",
                "GP4421000202", "GP4421000203", "GP4421000204", "GP4421000205", "GP4421000206", "GP4421000207",
                "GP4421000208", "GP4421000209", "GP4421000210", "GP4421000211", "GP4421000212", "GP4421000213",
                "GP4421000214", "GP4421000215", "GP4421000216", "GP4421000217", "GP4421000220", "GP4421000221",
                "GP4421000222", "GP4421000223", "GP4421000224", "GP4421000230", "GP4421000234", "GP4421000235",
                "GP4421000238", "GP4421000239", "GP4421000240", "GP4421000241", "GP4421000259", "GP4421000260",
                "GP4421000261", "GP4421000262", "GP4421000263", "GP4421000264", "GP4421000265", "GP4421000284",
                "GP4421000287", "GP4421000288", "GP4421000289", "GP4421000290", "GP4421000294", "GP4421000295",
                "GP4421000296", "GP4421000297", "GP4421000298", "GP4421000299", "GP4421000300", "GP4421000301",
                "GP4421000302", "GP4421000303", "GP4421000304", "GP4421000305", "GP4421000306", "GP4421000307",
                "GP4421000308", "GP4421000309", "GP4421000310", "GP4421000316", "GP4421000335", "GP4421000338",
                "GP4421000340", "GP4421000342", "GP4421000349", "GP4421000352");

        // 获得list与set的差集
        Collection rs = CollectionUtils.disjunction(list1, list2);
        // 将collection转换为list
        List<String> list3 = new ArrayList<>(rs);

        System.out.println(list3);
    }
}