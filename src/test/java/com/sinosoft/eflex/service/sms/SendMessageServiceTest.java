package com.sinosoft.eflex.service.sms;

import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.sinosoft.eflex.model.sendmessage.SendEmailContent;
import com.sinosoft.eflex.model.sendmessage.SendEmailReq;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.enums.EmailTemplateNoEnum;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;

/**
 * <AUTHOR>
 * @date 2020/12/21 22:25
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("uat")
public class SendMessageServiceTest {

    @Autowired
    private SendMessageService sendMessageService;

    @Test
    public void sendSMS() {
        SendSMSReq sendSMSReq = new SendSMSReq();

        // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_001.getCode());
        // sendSMSReq.setPhones("13928011602");
        // Map<String, Object> map = new HashMap<>();
        // map.put("grp_cont_no", "GP4421900071");
        // map.put("get_address", "广东省-珠海市-香洲区-丹东街夕阳路");
        // map.put("zipcode", "100000");
        // map.put("receiver", "何丹");
        // map.put("tel_phone", "13928011602");
        // map.put("deal_date", "2021年3月14日");
        // sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_002.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("grp_name","马大壮");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_003.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("grp_name","马大壮");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_004.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("grp_name","马大壮");
//        map.put("audit_opinion","不想给你投保");
//        sendSMSReq.setParam(map);

        // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_005.getCode());
        // sendSMSReq.setPhones("18931236803");
        // Map<String,Object> map = new HashMap<>();
        // map.put("ensure_name","萨瓦迪卡");
        // map.put("ensure_end_date","“2020-12-29”");
        // sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_006.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("ensure_name","萨瓦迪卡");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_007.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("validate_code","6789");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_008.getCode());
//        sendSMSReq.setPhones("16601117074");
//        Map<String,Object> map = new HashMap<>();
//        map.put("grp_name","马大壮");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_009.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("password","mdz123456");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_010.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("grp_name","“马大壮”");
//        map.put("tprtno","“123456789”");
//        map.put("totalPrem","“100”");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_011.getCode());
//        sendSMSReq.setPhones("18931236803");
//        Map<String,Object> map = new HashMap<>();
//        map.put("grp_name","“马大壮”");
//        map.put("ensure_name","“保一辈子险”");
//        sendSMSReq.setParam(map);

//        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_012.getCode());
        // sendSMSReq.setPhones("16601117074");
//        Map<String,Object> map = new HashMap<>();
//        map.put("grp_name","谢锡仁");
//        map.put("edor_app_no","GP111111111");
//        sendSMSReq.setParam(map);

        // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_013.getCode());
        // sendSMSReq.setPhones("16601117074");
        // Map<String, Object> map = new HashMap<>();
        // map.put("roleName", "初审岗，复审岗");
        // map.put("userName", "wudezhong");
        // map.put("userInitPassword", "111111");
        // sendSMSReq.setParam(map);

        // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_014.getCode());
        // sendSMSReq.setPhones("16601117074");
        // Map<String,Object> map = new HashMap<>();
        // map.put("roleName","复审岗");
        // sendSMSReq.setParam(map);

        // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_016.getCode());
        // sendSMSReq.setPhones("15512779827");
        // Map<String,Object> map = new HashMap<>();
        // map.put("edor_type","「保全增人」");
        // sendSMSReq.setParam(map);

        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_017.getCode());
        sendSMSReq.setPhones("16601117074");
        Map<String, Object> map = new HashMap<>();
        map.put("edor_type", "「保全增人」");
        sendSMSReq.setParam(map);

        sendMessageService.sendSMS(sendSMSReq);
    }



    @Test
    public void sendEmail() {
        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setTemplateNo(EmailTemplateNoEnum.STAFF_BENEFITS_015.getCode());
        Map<String,Object> map = new HashMap<>();
        map.put("ensure_name","武大径");
        map.put("person_name","五小井");
        map.put("per_name","陈大大");
        map.put("per_mobilephone","15028035623");
        sendEmailReq.setParam(map);
        SendEmailContent sendEmailContent = new SendEmailContent();
        sendEmailContent.setToMails("<EMAIL>");
        sendEmailContent.setTitle("主题");
//        sendEmailContent.setFileList(Arrays.asList("1111.xls"));
        sendEmailReq.setContent(sendEmailContent);
        sendMessageService.sendEmail(sendEmailReq);
    }

}