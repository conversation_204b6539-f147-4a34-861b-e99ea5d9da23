package com.sinosoft.eflex.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * <AUTHOR>
 * @date 2021/7/23 9:53
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("uat")
public class UserConfigManageServiceTest {

    @Test
    public void selectAuditUser() {
        // String s1 = "1";
        // String s2 = "1,2,3";
        // List<String> stringList1 = Arrays.asList(s1.split(","));
        // List<String> stringList2 = Arrays.asList(s2.split(","));
        // System.out.println(0);
        String name = "姓名";
        if (name.matches("^[\\u4e00-\\u9fa5]+$")) {
            System.out.println("1541545");
        }
    }

    @Test
    public void checkSelectAuditUserReq() {
    }

    @Test
    public void selectSingleAuditUser() {
    }

    @Test
    public void dealAuditUser() {
    }

    @Test
    public void checkauditUserInfo() {
    }

    @Test
    public void saveAuditUserInfo() {
    }

    @Test
    public void deleteSingleAuditUser() {
    }
}