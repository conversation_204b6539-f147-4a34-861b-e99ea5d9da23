package com.sinosoft.eflex.service;

import com.sinosoft.eflex.util.DateTimeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.EflexApplication;
import com.sinosoft.eflex.model.FCMailInfo;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/15 18:59
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("uat")
public class InsureServiceTest {

    @Autowired
    private InsureService insureService;

    @Test
    public void referMailInfo() {
        String s1 = null;
        Map map = new HashMap();
        map.put("s1", s1);
        System.out.println(JSONObject.toJSONString(map));
    }

    /**
     * Java 8 LocalDateTime的用法
     */
    @Test
    public void test1() {
        /**
         * 获取当前时间
         */
        LocalDateTime currentDateTime = LocalDateTime.now();
        System.out.println("当前时刻：" + currentDateTime);
        System.out.println("当前年份：" + currentDateTime.getYear());
        System.out.println("当前月份：" + currentDateTime.getMonth());
        System.out.println("当前日份：" + currentDateTime.getDayOfMonth());
        System.out.println("当前时：" + currentDateTime.getHour());
        System.out.println("当前分：" + currentDateTime.getMinute());
        System.out.println("当前秒：" + currentDateTime.getSecond());

        /**
         * 想构造：2021年12月24日16时58分32秒
         */
        LocalDateTime styleTime = LocalDateTime.of(2021, 12, 24, 16, 58, 32);
        System.out.println("自定义构造的日期" + styleTime);

        /**
         * 修改日期
         */

        LocalDateTime rightNow = LocalDateTime.now();
        // 减少 2 年
        rightNow = rightNow.minusYears(2);
        // 增加 3 个月
        rightNow = rightNow.plusMonths(3);
        // 直接修改年份到2008年
        rightNow = rightNow.withYear(2008);
        // 直接修改小时到13时
        rightNow = rightNow.withHour(13);
        System.out.println("修改后的日期：" + rightNow);

        /**
         * 格式化日期
         */
        LocalDateTime rightNow1 = LocalDateTime.now();

        String result1 = rightNow1.format(DateTimeFormatter.ISO_DATE_TIME);
        String result2 = rightNow1.format(DateTimeFormatter.BASIC_ISO_DATE);
        String result3 = rightNow1.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 输出结果：
        // 格式化后的日期(基本样式一举例)：2021-12-24T17:18:31.312
        // 格式化后的日期(基本样式二举例)：20211224
        // 格式化后的日期(自定义样式举例)：2021-12-24 17:18:31
        System.out.println("格式化后的日期(基本样式一举例)：" + result1);
        System.out.println("格式化后的日期(基本样式二举例)：" + result2);
        System.out.println("格式化后的日期(自定义样式举例)：" + result3);

        /**
         * 时间反解析
         */
        LocalDateTime time = LocalDateTime.parse("2002-01-02 11:21:10", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        System.out.println("字符串反解析后的时间为：" + time);


    }

    public static void main(String [] args){
        //非负数
        System.out.println("1".matches("^[0-9]*$"));

        System.out.println(DateTimeUtil.getCurrentAge("2017-06-21","2020-02-01"));

        //整数
        if ("-1".matches("^\\d+$")) {
            System.out.print("22222");
        }
        //数字或字母
        /*if (("asdasd23213").matches("^[a-z0-9A-Z]+$")){
            System.out.print("22222");
        }*/
        //整数或者一位小数
       /* String regexs = "^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{0,1})?$";
        if ("8.88".matches(regexs)){
            System.out.print("22222");
        }*/

        /*System.out.println("/home/<USER>/new/2019/11/12/0304/增加被保险人/护照.tif".substring(0,"/home/<USER>/new/2019/11/12/0304/增加被保险人/护照.tif".lastIndexOf("/")+1));*/

        System.out.print("2".matches("^0\\.[0-9]{1,2}$|^0{1}$|^1{1}$|^1\\.[0]{1,2}$"));
    }

}