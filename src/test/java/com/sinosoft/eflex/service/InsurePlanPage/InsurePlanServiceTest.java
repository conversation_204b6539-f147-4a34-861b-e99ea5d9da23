package com.sinosoft.eflex.service.InsurePlanPage;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.dao.FcPlanRiskInfoMapper;
import com.sinosoft.eflex.model.ChoiceInsurePeopleNextStepReq;
import com.sinosoft.eflex.model.confirmInsureEflex.PeopleInsureEflexPlanInfo;
import com.sinosoft.eflex.model.insureEflexPlanPage.InsureRiskInfo;
import com.sinosoft.eflex.model.insurePlanPage.InsurePlanPageReq;
import com.sinosoft.eflex.util.IDCardUtil;
import com.sinosoft.eflex.enums.PlanObjectEnum;
import com.sinosoft.eflex.enums.RelationEnum;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/5/18 13:42
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootTest
@ActiveProfiles("uat")
public class InsurePlanServiceTest {

    @Autowired
    private InsurePlanService insurePlanService;
    @Autowired
    private FcPlanRiskInfoMapper fcPlanRiskInfoMapper;

    @Test
    public void insureEflexPlanPage() {

        String token = "8af7b3412ec847e280ccf962e3ba59bd";

        InsurePlanPageReq insurePlanPageReq = new InsurePlanPageReq();
        insurePlanPageReq.setPageSource("01");
        List<ChoiceInsurePeopleNextStepReq> choiceInsurePeopleNextStepReqs = new ArrayList<>();
        // 人员信息
        ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq = new ChoiceInsurePeopleNextStepReq();
        choiceInsurePeopleNextStepReq.setPlanObject(PlanObjectEnum.STAFF.getCode());
        choiceInsurePeopleNextStepReq.setRelation(RelationEnum.SELF.getCode());
        choiceInsurePeopleNextStepReq.setPersonid("00000000000000002115");
        choiceInsurePeopleNextStepReqs.add(choiceInsurePeopleNextStepReq);
        // 家属信息1
        ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq1 = new ChoiceInsurePeopleNextStepReq();
        choiceInsurePeopleNextStepReq1.setPlanObject(PlanObjectEnum.FAMILY.getCode());
        choiceInsurePeopleNextStepReq1.setRelation(RelationEnum.PARENT.getCode());
        choiceInsurePeopleNextStepReq1.setPersonid("00000000000000002124");
        choiceInsurePeopleNextStepReqs.add(choiceInsurePeopleNextStepReq1);
        // 家属信息2
        ChoiceInsurePeopleNextStepReq choiceInsurePeopleNextStepReq2 = new ChoiceInsurePeopleNextStepReq();
        choiceInsurePeopleNextStepReq2.setPlanObject(PlanObjectEnum.FAMILY.getCode());
        choiceInsurePeopleNextStepReq2.setRelation(RelationEnum.PARENT.getCode());
        choiceInsurePeopleNextStepReq2.setPersonid("00000000000000002126");
        choiceInsurePeopleNextStepReqs.add(choiceInsurePeopleNextStepReq2);

        insurePlanPageReq.setChoiceInsurePeopleNextStepReqs(choiceInsurePeopleNextStepReqs);

        System.out.println("哈哈哈===" + JSONObject.toJSONString(insurePlanPageReq));
        insurePlanService.insureEflexPlanPage(token, insurePlanPageReq);

    }

    @Test
    public void test1() {
        String ensureCode = "FL000000000000002164";
        List<InsureRiskInfo> insureRiskInfos = fcPlanRiskInfoMapper.selectRiskInfoByEnsureCode(ensureCode);
        System.out.println(JSONObject.toJSONString(insureRiskInfos));
    }

    @Test
    public void test2() {
        // String token = "8981f0377341459b9ab02a319f7c95f1";
        // String grpNo = "00000000000000000237";
        // String ensureCode = "FL000000000000001373";
        // String perNo = "00000000000000018246";
        // insureService.selectionEnsure(token,grpNo,ensureCode,perNo);

        String ss = "(123)";
        System.out.println(ss.substring(ss.indexOf("(") + 1, ss.indexOf(")")));
    }

    @Test
    public void test3() {
        List<String> list = new ArrayList<>();
        list.add("00000000000000001123");
        list.add("00000000000000001125");
        list.add("00000000000000001126");
        System.out.println(JSONObject.toJSONString(list));
    }

    @Test
    public void test4() {
        String token = "";
        List<PeopleInsureEflexPlanInfo> peopleInsureEflexPlanInfos = new ArrayList<>();
        PeopleInsureEflexPlanInfo peopleInsureEflexPlanInfo = new PeopleInsureEflexPlanInfo();
        peopleInsureEflexPlanInfo.setName("元一");
        peopleInsureEflexPlanInfo.setPersonId("00000000000000002115");
        peopleInsureEflexPlanInfos.add(peopleInsureEflexPlanInfo);
        insurePlanService.confirmInsureEflex(token, peopleInsureEflexPlanInfos);
    }

    @Test
    public void test5() {
        IDCardUtil.isIDCard("130444199807081913");
    }

    public static void main(String[] args){
        LisIDEA lis = new LisIDEA();
        String str1 = lis.decryptString("1A02783195CE061AD5CD180615AE61F4");
        System.out.println("解密后字符串：" + str1);
        String replace = UUID.randomUUID().toString().replace("-", "");
        System.out.println("replace = " + replace);
        String str3 = lis.encryptString("146435");
        System.out.println("加密后字符串：" + str3);
    }

}