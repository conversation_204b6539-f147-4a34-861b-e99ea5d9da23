questions:
- variable: deploy.replicas
  label: "部署副本数"
  group: "部署设置"
  description: "deploy.replicas"
  default: "1"
  required: true
  type: int
- variable: profiles.active
  label: "运行环境"
  group: "部署设置"
  description: "profiles.active: dat ,uat,prd"
  default: "dat"
  required: true
  type: string

- variable: ingress.host
  label: "访问域名"
  group: "部署设置"
  description: "ingress"
  default: "eflex.tst.e-hqins.com"
  required: true
  type: string

- variable: nacos.config.url
  label: "Nacos配置中心地址"
  group: "配置中心"
  description: "nacos.config.url"
  default: "nacos.tst.e-hqins.com:80"
  required: true
  type: string

- variable: nacos.config.namespace
  label: "Nacos配置中心命名空间"
  group: "配置中心"
  description: "nacos.config.namespace"
  default: "8643ec67-08dd-4f80-8bc9-def107081624"
  required: true
  type: string