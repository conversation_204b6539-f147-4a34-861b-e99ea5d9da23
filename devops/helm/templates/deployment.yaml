apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
spec:
  replicas: {{ .Values.deploy.replicas }}
  minReadySeconds: 120
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  selector:
    matchLabels:
      app: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}
    spec:
      imagePullSecrets:
      - name: {{ .Values.deploy.imagePullSecret }}
      containers:
      - name: {{ .Release.Name }}
        image: {{ .Values.image }}
        volumeMounts:
          - name: yuanfu-service-pv-all
            mountPath: "/home/<USER>/hqeflex"
          - name: config
            mountPath: /bootstrap.yaml
            subPath: bootstrap.yaml
        env:
        - name: "spring.profiles.active"
          value: "{{ .Values.profiles.active }}"
        - name: "TZ"
          value: "Asia/Shanghai"
        ports:
        - containerPort: 8081
        command:
        - "java"
        - "-Xmx1024m"
        - "-Xms1024m"
        - "-Duser.timezone=GMT+08"
        - "-XX:MetaspaceSize=256M"
        - "-XX:MaxMetaspaceSize=256M"
        - "-XX:SurvivorRatio=4"
        - "-Dfile.encoding=UTF-8"
        - "-Dspring.cloud.nacos.config.server-addr={{ .Values.nacos.config.url }}"
        - "-Dspring.cloud.nacos.config.namespace={{ .Values.nacos.config.namespace }}"
        - "-jar"
        - "/eflex-service.jar"
        - "--spring.profiles.active={{ .Values.profiles.active }}"
        readinessProbe:
          httpGet:
            path: /info
            port: 8081
          initialDelaySeconds: 15
          timeoutSeconds: 5
      volumes:
        - name: yuanfu-service-pv-all
          persistentVolumeClaim:
            claimName: 	yuanfu-service-all-pvc
        - name: config
          configMap:
            name: {{ .Release.Name }}
            items:
              - key: bootstrap.yaml
                path: bootstrap.yaml
