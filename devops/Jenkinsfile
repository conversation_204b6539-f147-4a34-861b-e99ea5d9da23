import java.text.SimpleDateFormat
pipeline {
    agent {
        label 'master'
    }
    parameters {
         choice(
             name: 'ENV',
             choices: ['dat', 'uat','prd'],
             description: '选择编构建环境：\n1，dat\n2，uat '
         )
    }

    tools {
        maven "mvn3"
    }

    environment {
        // app
        APP_NAME = "eflex-service"
		APPLICATION_VERSION = ""
        VERSION = ""

        // git
        CREDENTIALS_ID = "ab28a135-bb7a-4c1d-8d9e-4855466dd540"
        GIT_USER = "deployer"
        GIT_EMAIL = "<EMAIL>"
        TAG = ""

        // maven
        MAVEN_CONFIG = ""

        // docker
        DOCKER_ID = ""
        DOCKER_URL = ""
        DOCKER_REGISTRY = ""
        PREFIX = ""
        JENKINS_HOME1=""
        //服务器目录定义
        // helm repository
        HELM_REPOSITORY=""
    }
    stages {
        // 设置环境变量
        stage("set environment") {
            steps {
                script {
                    if(ENV == 'test') {
                        MAVEN_CONFIG = "ef3e7f13-6c7f-43b6-9dff-61212c9ec8a5"
                        DOCKER_ID = "************************************"
                        DOCKER_URL = "https://nexus-test.hqins.cn:5080/"
                        DOCKER_REGISTRY = "nexus-test.hqins.cn:5080"
                        PREFIX = ENV
                        HELM_REPOSITORY = JENKINS_HOME + "/helm-repository/testHelmChart"
                    }else if(ENV == 'prd' || ENV == 'uat') {
                        MAVEN_CONFIG = "maven-settings-uat"
                        DOCKER_ID = "************************************"
                        DOCKER_URL = "https://docker.e-hqins.com/"
                        DOCKER_REGISTRY = "docker.e-hqins.com"
                        HELM_REPOSITORY= JENKINS_HOME + "/helm-repository/helmchats"
                        PREFIX = "release"
                    }else {
                        sh "exit 1"
                    }

                    // 获取应用版本
                    def pom = readMavenPom file: "./pom.xml"
                    APPLICATION_VERSION = "${pom.version}".replace("-SNAPSHOT","").toLowerCase()

                    //获取部署版本
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                    Calendar lastDate = Calendar.getInstance();
                    TAG = sdf.format(lastDate.getTime());
                    VERSION = PREFIX  +  '.' + APPLICATION_VERSION + "."  + TAG[0..-4]
                }
            }
        }

        // 编译项目代码 mvn
        stage("Build") {
            steps {
                withMaven(maven: "mvn3", mavenSettingsConfig: MAVEN_CONFIG) {
                    sh " mvn clean package -DskipTests"
                }
            }
        }

        // 制作项目镜像
        stage("Docker Build") {
		    steps {
                withDockerRegistry(credentialsId: DOCKER_ID, url: DOCKER_URL) {
			    sh """
                    cd ./devops/docker
                    rm -rf ${APP_NAME}.jar
                    cp ../../target/*.jar .

					docker build -t ${DOCKER_REGISTRY}/${APP_NAME}:${VERSION} .
					docker push ${DOCKER_REGISTRY}/${APP_NAME}:${VERSION}
				"""
               }
            }
        }

        // 项目代码打tag
//         stage("Generate Tag") {
//             steps {
//                 script {
//                     if(ENV == 'uat') {
//                         wrap([$class: 'BuildUser']) {
//                             sshagent([CREDENTIALS_ID]) {
//                                 sh """
//                                     ssh-add ${HELM_REPOSITORY}/.git/.ssh/id_rsa
//                                     git config user.email ${GIT_EMAIL}
//                                     git config user.name ${GIT_USER}
//                                     git branch -v
//                                     git tag -a ${TAG} -m 'Jenkins_Build'
//                                     git push origin ${TAG}
//                                 """
//                             }
//                         }
//                     }
//                 }
//             }
//         }

        // 制作helm的chart
        stage("Helm Update"){
                steps{
                    sh """
                      cd ${HELM_REPOSITORY}/
                      git pull
                      cd ./charts/
                      if [ ! -d ${APP_NAME}  ];then
                        mkdir ${APP_NAME}
                      fi
                      cd ${APP_NAME}
                      mkdir ${VERSION}
                      cd ${VERSION}
                      cp -rf ${WORKSPACE}/devops/helm/* .
                      sed -i 's/<APP_NAME>/${APP_NAME}/g' ./Chart.yaml
                      sed -i 's/<VERSION>/${VERSION}/g' ./Chart.yaml
                      sed -i 's/<APPLICATION_VERSION>/${APPLICATION_VERSION}/g' ./Chart.yaml

                      sed -i 's/<VERSION>/${VERSION}/g' ./values.yaml
                      sed -i 's/<APP_NAME>/${APP_NAME}/g' ./values.yaml
                      sed -i 's/<DOCKER_REGISTRY>/${DOCKER_REGISTRY}/g' ./values.yaml
                      git add -A
                      git commit -m 'add helm chart'
                      git push origin
                     """
                }
        }
    }
}
